# EquitySendCommandStateServiceImpl#increaseSendCount 方法 Apollo 开关配置修改总结

## 修改内容

### 1. 在 MonitorConstants 中添加打点常量管理

**文件**: `promoequity-service/src/main/java/com/ddmc/promoequity/common/constant/MonitorConstants.java`

#### 添加的接口
```java
public interface EquitySendStatisticsIncrease {
    /**
     * 权益发送统计增加 - 开关打开
     */
    String SWITCH_ON = "equity_send_statistics_increase";
    /**
     * 权益发送统计增加 - 老逻辑
     */
    String OLD_LOGIC = "equity_send_statistics_increase";
}
```

### 2. 在 EquitySendCommandStateServiceImpl 类中添加 Apollo 开关配置

**文件**: `promoequity-service/src/main/java/com/ddmc/promoequity/infra/service/EquitySendCommandStateServiceImpl.java`

#### 添加的导入
```java
import com.ddmc.promoequity.common.constant.MonitorConstants;
import org.springframework.beans.factory.annotation.Value;
```

#### 添加的字段
```java
/**
 * EquitySendStatisticsIncrease 统计开关
 */
@Value("${promoequity.EquitySendStatisticsIncrease.switch:false}")
private boolean equitySendStatisticsIncreaseSwitch;
```

### 3. 修改 increaseSendCount 方法

在 `increaseSendCount` 方法的最开始（参数校验前面）添加 Apollo 开关控制逻辑：

```java
public void increaseSendCount(String commandId) {
    try {
        // Apollo 开关控制 - 放在最开始，参数校验前面
        if (equitySendStatisticsIncreaseSwitch) {
            CatUtils.logEvent(MonitorConstants.EquitySendStatisticsIncrease.SWITCH_ON, "switch_on", "success", commandId);
            log.info("EquitySendStatisticsIncrease switch is on, skip increaseSendCount logic, commandId: {}", commandId);
            return;
        }

        if (commandId == null) {
            return;
        }

        // 新老逻辑打点
        CatUtils.logEvent(MonitorConstants.EquitySendStatisticsIncrease.OLD_LOGIC, "old_logic", "success", commandId);

        // ... 原有的业务逻辑
    } catch (Exception e) {
        log.error("increaseSendCount-error,request:{}", commandId, e);
    }
}
```

## 功能说明

### Apollo 开关配置
- **开关名称**: `promoequity.EquitySendStatisticsIncrease.switch`
- **默认值**: `false` (关闭状态)
- **开关位置**: 在方法最开始，参数校验之前

### 打点逻辑
1. **开关打开时**:
   - 打点 type: `equity_send_statistics_increase` (统一小写，下划线分割)
   - 打点 name: `switch_on` (统一小写，下划线分割)
   - 直接返回，不执行后续统计逻辑

2. **开关关闭时**:
   - 打点 type: `equity_send_statistics_increase` (统一小写，下划线分割)
   - 打点 name: `old_logic` (统一小写，下划线分割)
   - 继续执行原有的统计逻辑

### 命名规范
- 开关名称使用 `EquitySendStatisticsIncrease` 开头，表明是 EquitySendStatisticsIncrease 统计相关功能
- 打点的 type 和 name 统一使用小写，用下划线分割
- 打点的 type 统一在 MonitorConstants 中管理

## 测试

创建了测试文件 `EquitySendCommandStateServiceImplTest.java` 来验证开关功能：
- 测试开关关闭时的正常逻辑
- 测试开关开启时直接返回
- 测试 null commandId 的处理

## 编译验证

修改后的代码已通过编译验证，无编译错误。
