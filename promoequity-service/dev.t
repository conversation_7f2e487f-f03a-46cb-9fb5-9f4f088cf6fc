
> Task :promoequity-service:dependencies

------------------------------------------------------------
Project :promoequity-service
------------------------------------------------------------

annotationProcessor - Annotation processors and their dependencies for source set 'main'.
No dependencies

apiElements - API elements for main. (n)
No dependencies

archives - Configuration for archive artifacts.
No dependencies

bootArchives - Configuration for Spring Boot archive artifacts.
No dependencies

compile - Dependencies for source set 'main' (deprecated, use 'implementation ' instead).
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
\--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED

compileClasspath - Compile classpath for source set 'main'.
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE
|    +--- org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE -> 2.1.4.RELEASE
|    |    +--- org.springframework.data:spring-data-commons:2.1.4.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- org.springframework:spring-tx:5.1.2.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework:spring-oxm:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-aop:5.1.2.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework:spring-context-support:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.25
\--- io.lettuce:lettuce-core:5.1.3.RELEASE
     +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
     +--- io.netty:netty-common:4.1.31.Final
     +--- io.netty:netty-transport:4.1.31.Final (*)
     \--- io.netty:netty-handler:4.1.31.Final (*)

compileOnly - Compile only dependencies for source set 'main'.
No dependencies

default - Configuration for default artifacts.
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE
|    +--- org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE -> 2.1.4.RELEASE
|    |    +--- org.springframework.data:spring-data-commons:2.1.4.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- org.springframework:spring-tx:5.1.2.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework:spring-oxm:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-aop:5.1.2.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework:spring-context-support:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.25
\--- io.lettuce:lettuce-core:5.1.3.RELEASE
     +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
     +--- io.netty:netty-common:4.1.31.Final
     +--- io.netty:netty-transport:4.1.31.Final (*)
     \--- io.netty:netty-handler:4.1.31.Final (*)

implementation - Implementation only dependencies for source set 'main'. (n)
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE (n)
\--- io.lettuce:lettuce-core:5.1.3.RELEASE (n)

runtime - Runtime dependencies for source set 'main' (deprecated, use 'runtimeOnly ' instead).
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
\--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED

runtimeClasspath - Runtime classpath of source set 'main'.
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE
|    +--- org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE -> 2.1.4.RELEASE
|    |    +--- org.springframework.data:spring-data-commons:2.1.4.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- org.springframework:spring-tx:5.1.2.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework:spring-oxm:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-aop:5.1.2.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework:spring-context-support:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.25
\--- io.lettuce:lettuce-core:5.1.3.RELEASE
     +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
     +--- io.netty:netty-common:4.1.31.Final
     +--- io.netty:netty-transport:4.1.31.Final (*)
     \--- io.netty:netty-handler:4.1.31.Final (*)

runtimeElements - Elements of runtime for main. (n)
No dependencies

runtimeOnly - Runtime only dependencies for source set 'main'. (n)
No dependencies

testAnnotationProcessor - Annotation processors and their dependencies for source set 'test'.
No dependencies

testCompile - Dependencies for source set 'test' (deprecated, use 'testImplementation ' instead).
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1 -> 2.6
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.boot:spring-boot-starter-test -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE
|    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0
|    |    +--- net.minidev:json-smart:2.3
|    |    |    \--- net.minidev:accessors-smart:1.2
|    |    |         \--- org.ow2.asm:asm:5.0.4
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- junit:junit:4.12 -> 4.13
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.assertj:assertj-core:3.11.1
|    +--- org.mockito:mockito-core:2.23.4
|    |    +--- net.bytebuddy:byte-buddy:1.9.3 -> 1.10.16
|    |    +--- net.bytebuddy:byte-buddy-agent:1.9.3 -> 1.10.16
|    |    \--- org.objenesis:objenesis:2.6
|    +--- org.hamcrest:hamcrest-core:1.3
|    +--- org.hamcrest:hamcrest-library:1.3
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-test:5.1.4.RELEASE
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.2
\--- junit:junit:4.13 (*)

testCompileClasspath - Compile classpath for source set 'test'.
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1 -> 2.6
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE
|    +--- org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE -> 2.1.4.RELEASE
|    |    +--- org.springframework.data:spring-data-commons:2.1.4.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- org.springframework:spring-tx:5.1.2.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework:spring-oxm:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-aop:5.1.2.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework:spring-context-support:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- io.lettuce:lettuce-core:5.1.3.RELEASE
|    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    +--- io.netty:netty-common:4.1.31.Final
|    +--- io.netty:netty-transport:4.1.31.Final (*)
|    \--- io.netty:netty-handler:4.1.31.Final (*)
+--- org.springframework.boot:spring-boot-starter-test -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE
|    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0
|    |    +--- net.minidev:json-smart:2.3
|    |    |    \--- net.minidev:accessors-smart:1.2
|    |    |         \--- org.ow2.asm:asm:5.0.4
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- junit:junit:4.12 -> 4.13
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.assertj:assertj-core:3.11.1
|    +--- org.mockito:mockito-core:2.23.4
|    |    +--- net.bytebuddy:byte-buddy:1.9.3 -> 1.10.16
|    |    +--- net.bytebuddy:byte-buddy-agent:1.9.3 -> 1.10.16
|    |    \--- org.objenesis:objenesis:2.6
|    +--- org.hamcrest:hamcrest-core:1.3
|    +--- org.hamcrest:hamcrest-library:1.3
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-test:5.1.4.RELEASE
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.2
\--- junit:junit:4.13 (*)

testCompileOnly - Compile only dependencies for source set 'test'.
No dependencies

testImplementation - Implementation only dependencies for source set 'test'. (n)
No dependencies

testRuntime - Runtime dependencies for source set 'test' (deprecated, use 'testRuntimeOnly ' instead).
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1 -> 2.6
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.boot:spring-boot-starter-test -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE
|    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0
|    |    +--- net.minidev:json-smart:2.3
|    |    |    \--- net.minidev:accessors-smart:1.2
|    |    |         \--- org.ow2.asm:asm:5.0.4
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- junit:junit:4.12 -> 4.13
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.assertj:assertj-core:3.11.1
|    +--- org.mockito:mockito-core:2.23.4
|    |    +--- net.bytebuddy:byte-buddy:1.9.3 -> 1.10.16
|    |    +--- net.bytebuddy:byte-buddy-agent:1.9.3 -> 1.10.16
|    |    \--- org.objenesis:objenesis:2.6
|    +--- org.hamcrest:hamcrest-core:1.3
|    +--- org.hamcrest:hamcrest-library:1.3
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-test:5.1.4.RELEASE
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.2
\--- junit:junit:4.13 (*)

testRuntimeClasspath - Runtime classpath of source set 'test'.
+--- org.springframework.boot:spring-boot-starter-web -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.1.2.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE
|    |    |    |    \--- org.springframework:spring-jcl:5.1.4.RELEASE
|    |    |    \--- org.springframework:spring-context:5.1.4.RELEASE
|    |    |         +--- org.springframework:spring-aop:5.1.4.RELEASE
|    |    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE
|    |    |         |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |         \--- org.springframework:spring-expression:5.1.4.RELEASE
|    |    |              \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.1.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.11.1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.25
|    |    |         \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.23
|    +--- org.springframework.boot:spring-boot-starter-json:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.1.4.RELEASE
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.8
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.1.2.RELEASE
|    |    +--- javax.annotation:javax.annotation-api:1.3.2
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.14
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.14
|    +--- org.hibernate.validator:hibernate-validator:6.0.14.Final
|    |    +--- javax.validation:validation-api:2.0.1.Final
|    |    +--- org.jboss.logging:jboss-logging:3.3.2.Final
|    |    \--- com.fasterxml:classmate:1.3.4 -> 1.4.0
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.1.4.RELEASE
|         +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-expression:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-web:5.1.4.RELEASE (*)
+--- org.springframework.cloud:spring-cloud-context -> 2.1.0.RELEASE
|    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.1.2.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.8 (*)
|    \--- io.micrometer:micrometer-core:1.1.2
|         +--- org.hdrhistogram:HdrHistogram:2.1.9
|         \--- org.latencyutils:LatencyUtils:2.0.3
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE
|    |    |    \--- org.springframework.security:spring-security-crypto:5.1.3.RELEASE
|    |    \--- org.springframework.security:spring-security-rsa:1.0.7.RELEASE
|    |         \--- org.bouncycastle:bcpkix-jdk15on:1.60
|    |              \--- org.bouncycastle:bcprov-jdk15on:1.60
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2
|    |    \--- io.github.openfeign.form:feign-form-spring:3.5.0 -> 3.8.0
|    |         +--- io.github.openfeign.form:feign-form:3.8.0
|    |         |    \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |         +--- org.springframework:spring-web:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- commons-fileupload:commons-fileupload:1.4
|    |         |    \--- commons-io:commons-io:2.2 -> 2.6
|    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    +--- org.springframework:spring-web:5.1.4.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    +--- io.github.openfeign:feign-core:10.1.0
|    +--- io.github.openfeign:feign-slf4j:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.13 -> 1.7.25
|    +--- io.github.openfeign:feign-hystrix:10.1.0
|    |    +--- io.github.openfeign:feign-core:10.1.0
|    |    +--- com.netflix.archaius:archaius-core:0.6.6 -> 0.7.6
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- commons-configuration:commons-configuration:1.8
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|    |    |    +--- com.google.guava:guava:16.0 -> 30.1-android
|    |    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    |    +--- org.checkerframework:checker-compat-qual:2.5.5
|    |    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.9.0
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.9.8
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.9.8 (*)
|    |    \--- com.netflix.hystrix:hystrix-core:1.4.26 -> 1.5.18
|    |         +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.25
|    |         +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.6 (*)
|    |         +--- io.reactivex:rxjava:1.2.0
|    |         \--- org.hdrhistogram:HdrHistogram:2.1.9
|    \--- io.github.openfeign:feign-java8:10.1.0
|         \--- io.github.openfeign:feign-core:10.1.0
+--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery -> 2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-commons:2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    +--- org.springframework.cloud:spring-cloud-zookeeper-discovery:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-zookeeper-core:2.1.0.RELEASE
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1
|    |    +--- org.apache.curator:curator-recipes:4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1
|    |    |         \--- org.apache.curator:curator-client:4.0.1
|    |    |              +--- org.apache.zookeeper:zookeeper:3.5.3-beta -> 3.4.9
|    |    |              |    +--- org.slf4j:slf4j-api:1.6.1 -> 1.7.25
|    |    |              |    +--- log4j:log4j:1.2.16 -> 1.2.17
|    |    |              |    +--- jline:jline:0.9.94
|    |    |              |    \--- io.netty:netty:3.10.5.Final
|    |    |              +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |              \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- org.springframework.cloud:spring-cloud-netflix-core:2.1.0.RELEASE
|    |    \--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    |         \--- org.springframework.boot:spring-boot-starter-aop:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.1.0.RELEASE
|    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    \--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.1.0.RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.1.0.RELEASE (*)
|         +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.1.0.RELEASE (*)
|         +--- com.netflix.ribbon:ribbon:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|         |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0 -> 30.1-android (*)
|         |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    |    +--- commons-lang:commons-lang:2.6
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|         |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|         |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|         |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.2.0
|         |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|         |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25
|         |    |    |    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|         |    |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|         |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.25
|         |    |    |         \--- javax.inject:javax.inject:1
|         |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    +--- io.reactivex:rxnetty:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.25
|         |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|         |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|         |    |    +--- javax.inject:javax.inject:1
|         |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18 (*)
|         |    +--- javax.inject:javax.inject:1
|         |    +--- io.reactivex:rxjava:1.0.10 -> 1.2.0
|         |    +--- io.reactivex:rxnetty:0.4.9 (*)
|         |    +--- commons-configuration:commons-configuration:1.8 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    \--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|         |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|         |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         |    +--- commons-collections:commons-collections:3.2.2
|         |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.12
|         |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.10
|         |    |    +--- commons-logging:commons-logging:1.2
|         |    |    \--- commons-codec:commons-codec:1.11 -> 1.10
|         |    +--- com.sun.jersey:jersey-client:1.19.1
|         |    |    \--- com.sun.jersey:jersey-core:1.19.1
|         |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|         |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|         |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.12 (*)
|         |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|         |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.25
|         |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|         |    +--- com.google.guava:guava:16.0.1 -> 30.1-android (*)
|         |    +--- com.netflix.archaius:archaius-core:0.7.6 (*)
|         |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|         +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|         \--- io.reactivex:rxjava:1.2.0
+--- org.apache.zookeeper:zookeeper:3.4.9 (*)
+--- com.ctrip.framework.apollo:apollo-client:1.7.0
|    +--- com.ctrip.framework.apollo:apollo-core:1.7.0
|    |    +--- com.google.code.gson:gson:2.8.0 -> 2.8.5
|    |    +--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- com.google.inject:guice:4.1.0
|    |    +--- javax.inject:javax.inject:1
|    |    +--- aopalliance:aopalliance:1.0
|    |    \--- com.google.guava:guava:19.0 -> 30.1-android (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    \--- org.yaml:snakeyaml:1.17 -> 1.23
+--- com.ctrip.framework.apollo:apollo-core:1.7.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- com.zaxxer:HikariCP:3.2.0
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    \--- org.springframework:spring-jdbc:5.1.4.RELEASE
|         +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|         +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|         \--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE
|              +--- org.springframework:spring-beans:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|              \--- org.springframework:spring-core:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
+--- org.springframework:spring-tx:5.1.5.RELEASE (*)
+--- mysql:mysql-connector-java:5.1.47
+--- com.baomidou:mybatis-plus-boot-starter:*******
|    +--- com.baomidou:mybatis-plus:*******
|    |    \--- com.baomidou:mybatis-plus-extension:*******
|    |         +--- com.baomidou:mybatis-plus-core:*******
|    |         |    +--- com.baomidou:mybatis-plus-annotation:*******
|    |         |    +--- com.github.jsqlparser:jsqlparser:4.0
|    |         |    \--- org.mybatis:mybatis:3.5.7
|    |         \--- org.mybatis:mybatis-spring:2.0.6
|    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.5 -> 2.1.2.RELEASE (*)
|    \--- org.springframework.boot:spring-boot-starter-jdbc:2.4.5 -> 2.1.2.RELEASE (*)
+--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE
|    |    +--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE
|    |    |    +--- io.netty:netty-all:4.1.31.Final
|    |    |    +--- log4j:log4j:1.2.17
|    |    |    +--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- org.apache.logging.log4j:log4j-core:2.3 -> 2.11.1
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.11.1
|    |    |    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    |    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    |    +--- commons-io:commons-io:2.6
|    |    |    +--- org.aspectj:aspectjrt:1.9.4 -> 1.9.2
|    |    |    +--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    |    +--- net.bytebuddy:byte-buddy:1.10.16
|    |    |    \--- net.bytebuddy:byte-buddy-agent:1.10.16
|    |    +--- com.ddmc:ddmc-monitor-mysql6:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- com.ddmc:ddmc-monitor-mysql8:1.1.13-RELEASE
|    |    |    \--- com.ddmc:ddmc-monitor-core:1.1.13-RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.2.RELEASE -> 2.1.0.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-stream:2.1.0.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter-validation:2.1.1.RELEASE -> 2.1.2.RELEASE
|    |         |    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.14
|    |         |    \--- org.hibernate.validator:hibernate-validator:6.0.14.Final (*)
|    |         +--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE
|    |         |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |         |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-core:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |         |    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE
|    |         |    |    \--- org.springframework:spring-core:5.0.7.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- io.projectreactor:reactor-core:3.2.5.RELEASE
|    |         |         \--- org.reactivestreams:reactive-streams:1.0.2
|    |         +--- org.springframework.integration:spring-integration-jmx:5.1.1.RELEASE -> 5.1.2.RELEASE
|    |         |    \--- org.springframework.integration:spring-integration-core:5.1.2.RELEASE (*)
|    |         +--- org.springframework:spring-tuple:1.0.0.RELEASE
|    |         |    +--- com.esotericsoftware:kryo-shaded:3.0.3
|    |         |    |    +--- com.esotericsoftware:minlog:1.3.0
|    |         |    |    \--- org.objenesis:objenesis:2.1 -> 2.6
|    |         |    +--- org.springframework:spring-core:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.6.6 -> 2.9.8 (*)
|    |         |    \--- org.springframework:spring-context:4.2.6.RELEASE -> 5.1.4.RELEASE (*)
|    |         +--- org.springframework.integration:spring-integration-tuple:1.0.0.RELEASE
|    |         |    +--- org.springframework:spring-tuple:1.0.0.RELEASE (*)
|    |         |    \--- org.springframework.integration:spring-integration-core:4.2.5.RELEASE -> 5.1.2.RELEASE (*)
|    |         +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    |         \--- org.springframework.cloud:spring-cloud-function-context:2.0.0.RELEASE
|    |              +--- org.springframework.boot:spring-boot-autoconfigure:2.1.1.RELEASE -> 2.1.2.RELEASE (*)
|    |              +--- org.springframework.cloud:spring-cloud-function-core:2.0.0.RELEASE
|    |              |    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    |              |    \--- org.springframework:spring-core:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    |              \--- org.springframework:spring-messaging:5.1.3.RELEASE -> 5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
+--- com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE
|    +--- com.zaxxer:HikariCP:3.1.0 -> 3.2.0 (*)
|    +--- com.ddmc:ddmc-monitor:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.25
|    +--- org.apache.curator:curator-framework:4.0.1 (*)
|    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    +--- org.apache.curator:curator-x-discovery:4.0.1 (*)
|    +--- commons-beanutils:commons-beanutils:1.9.4
|    |    +--- commons-logging:commons-logging:1.2
|    |    \--- commons-collections:commons-collections:3.2.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.0 -> 2.9.8 (*)
|    +--- mysql:mysql-connector-java:5.1.34 -> 5.1.47
|    \--- com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE (*)
+--- io.springfox:springfox-swagger2:2.7.0
|    +--- io.swagger:swagger-annotations:1.5.13
|    +--- io.swagger:swagger-models:1.5.13
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.8.5 -> 2.9.0
|    |    +--- org.slf4j:slf4j-api:1.7.22 -> 1.7.25
|    |    \--- io.swagger:swagger-annotations:1.5.13
|    +--- io.springfox:springfox-spi:2.7.0
|    |    \--- io.springfox:springfox-core:2.7.0
|    |         +--- net.bytebuddy:byte-buddy:1.6.14 -> 1.10.16
|    |         +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |         +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |         +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |         +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE
|    |         |    +--- org.springframework:spring-beans:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-context:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    +--- org.springframework:spring-aop:4.0.9.RELEASE -> 5.1.4.RELEASE (*)
|    |         |    \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    |         \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE
|    |              +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |              \--- org.slf4j:slf4j-api:1.7.10 -> 1.7.25
|    +--- io.springfox:springfox-schema:2.7.0
|    |    +--- io.springfox:springfox-core:2.7.0 (*)
|    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    +--- io.springfox:springfox-swagger-common:2.7.0
|    |    +--- io.swagger:swagger-annotations:1.5.13
|    |    +--- io.swagger:swagger-models:1.5.13 (*)
|    |    +--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- io.springfox:springfox-schema:2.7.0 (*)
|    |    +--- io.springfox:springfox-spring-web:2.7.0
|    |    |    +--- org.reflections:reflections:0.9.11
|    |    |    |    +--- com.google.guava:guava:20.0 -> 30.1-android (*)
|    |    |    |    \--- org.javassist:javassist:3.21.0-GA
|    |    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    |    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    |    |    \--- io.springfox:springfox-spi:2.7.0 (*)
|    |    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    |    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    |    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    |    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    |    \--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    +--- io.springfox:springfox-spring-web:2.7.0 (*)
|    +--- com.google.guava:guava:18.0 -> 30.1-android (*)
|    +--- com.fasterxml:classmate:1.3.3 -> 1.4.0
|    +--- org.slf4j:slf4j-api:1.7.24 -> 1.7.25
|    +--- org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE (*)
|    +--- org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE (*)
|    \--- org.mapstruct:mapstruct:1.1.0.Final
+--- io.springfox:springfox-swagger-ui:2.7.0
|    \--- io.springfox:springfox-spring-web:2.7.0 (*)
+--- log4j:log4j:1.2.17
+--- org.springframework.kafka:spring-kafka:2.2.3.RELEASE
|    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework.retry:spring-retry:1.2.2.RELEASE -> 1.2.3.RELEASE (*)
|    \--- org.apache.kafka:kafka-clients:2.0.1
|         +--- org.lz4:lz4-java:1.4.1
|         +--- org.xerial.snappy:snappy-java:1.1.7.1
|         \--- org.slf4j:slf4j-api:1.7.25
+--- com.github.danielwegener:logback-kafka-appender:0.2.0-RC2
|    +--- org.apache.kafka:kafka-clients:1.0.0 -> 2.0.1 (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-log:1.0.10-RELEASE
|    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    +--- com.ddmc:ddmc-monitor-core:1.0.5-RELEASE -> 1.1.13-RELEASE (*)
|    \--- org.codehaus.janino:janino:3.0.6 -> 3.0.11
|         \--- org.codehaus.janino:commons-compiler:3.0.11
+--- org.slf4j:slf4j-api:1.7.25
+--- org.slf4j:log4j-over-slf4j:1.7.25
|    \--- org.slf4j:slf4j-api:1.7.25
+--- com.ddmc:ddmc-monitor:1.1.13-RELEASE (*)
+--- com.ddmc.soa:spring-cloud-ddmc:1.2.6-RELEASE
|    +--- org.springframework:spring-context:5.1.5.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-context:2.1.0.RELEASE (*)
|    +--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- org.slf4j:slf4j-api:1.7.25
|    +--- log4j:log4j:1.2.17
|    +--- com.ddmc:ddmc-monitor:1.1.18-RELEASE -> 1.1.13-RELEASE (*)
|    +--- com.ddmc:ddmc-graceful-shutdown:1.0.7-RELEASE
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:2.1.2.RELEASE (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-zookeeper-discovery:2.1.0.RELEASE (*)
|    +--- com.ctrip.framework.apollo:apollo-client:1.1.0 -> 1.7.0 (*)
|    +--- org.mydotey.lang:lang-extension:1.2.1
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-core:1.6.4
|    |    +--- org.mydotey.lang:lang-extension:1.2.0 -> 1.2.1 (*)
|    |    \--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    +--- org.mydotey.scf:scf-simple:1.6.4
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    \--- org.mydotey.scf:scf-core:1.6.4 (*)
|    +--- org.mydotey.scf:scf-apollo:1.6.2
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- com.ctrip.framework.apollo:apollo-client:1.0.0 -> 1.7.0 (*)
|    +--- org.mydotey.caravan:caravan-util:2.0.3
|    |    +--- org.mydotey.lang:lang-extension:1.2.1 (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.25
|    |    +--- org.mydotey.scf:scf-simple:1.6.4 (*)
|    |    \--- org.mydotey.circularbuffer:circular-buffer:1.0.0
|    +--- org.mydotey.codec:jackson-codec-util:1.1.0
|    |    +--- org.mydotey.codec:codec-util:1.1.0
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.9.7 -> 2.9.8
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.7 -> 2.9.0
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.7 -> 2.9.8 (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.9.8
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.9.8 -> 2.9.0
|    +--- com.fasterxml.jackson.core:jackson-databind:2.9.8 (*)
|    +--- com.csoss:monitor-agent:1.0.5-RELEASE
|    |    +--- com.csoss:monitor-instrumentation:1.0.5-RELEASE
|    |    |    +--- com.csoss:monitor-trace:1.0.5-RELEASE
|    |    |    |    +--- com.csoss:monitor-metrics:1.0.5-RELEASE
|    |    |    |    |    \--- com.csoss:monitor-common:1.0.5-RELEASE
|    |    |    |    |         +--- io.grpc:grpc-netty:1.21.0
|    |    |    |    |         |    +--- io.grpc:grpc-core:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    +--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-api:[1.21.0] -> 1.21.0
|    |    |    |    |         |    |    |    +--- io.grpc:grpc-context:1.21.0
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2 -> 2.4.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.17 -> 1.19
|    |    |    |    |         |    |    |    \--- com.google.guava:guava:26.0-android -> 30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.code.gson:gson:2.7 -> 2.8.5
|    |    |    |    |         |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    |         |    |    \--- io.opencensus:opencensus-contrib-grpc-metrics:0.21.0
|    |    |    |    |         |    |         \--- io.opencensus:opencensus-api:0.21.0
|    |    |    |    |         |    +--- io.netty:netty-codec-http2:[4.1.34.Final] -> 4.1.31.Final
|    |    |    |    |         |    |    +--- io.netty:netty-codec-http:4.1.31.Final
|    |    |    |    |         |    |    |    \--- io.netty:netty-codec:4.1.31.Final
|    |    |    |    |         |    |    |         \--- io.netty:netty-transport:4.1.31.Final
|    |    |    |    |         |    |    |              +--- io.netty:netty-buffer:4.1.31.Final
|    |    |    |    |         |    |    |              |    \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    |              \--- io.netty:netty-resolver:4.1.31.Final
|    |    |    |    |         |    |    |                   \--- io.netty:netty-common:4.1.31.Final
|    |    |    |    |         |    |    \--- io.netty:netty-handler:4.1.31.Final
|    |    |    |    |         |    |         +--- io.netty:netty-buffer:4.1.31.Final (*)
|    |    |    |    |         |    |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |    |         \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |    \--- io.netty:netty-handler-proxy:4.1.34.Final -> 4.1.31.Final
|    |    |    |    |         |         +--- io.netty:netty-transport:4.1.31.Final (*)
|    |    |    |    |         |         +--- io.netty:netty-codec-socks:4.1.31.Final
|    |    |    |    |         |         |    \--- io.netty:netty-codec:4.1.31.Final (*)
|    |    |    |    |         |         \--- io.netty:netty-codec-http:4.1.31.Final (*)
|    |    |    |    |         +--- io.grpc:grpc-api:1.21.0 (*)
|    |    |    |    |         +--- io.opentelemetry:opentelemetry-proto:1.4.1-alpha
|    |    |    |    |         |    +--- com.google.protobuf:protobuf-java:3.17.2
|    |    |    |    |         |    +--- io.grpc:grpc-protobuf:1.38.0
|    |    |    |    |         |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    +--- com.google.protobuf:protobuf-java:3.12.0 -> 3.17.2
|    |    |    |    |         |    |    +--- com.google.api.grpc:proto-google-common-protos:2.0.1 -> 1.12.0
|    |    |    |    |         |    |    +--- io.grpc:grpc-protobuf-lite:1.38.0
|    |    |    |    |         |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         |    |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    |    +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |    |    +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |    \--- io.grpc:grpc-stub:1.38.0
|    |    |    |    |         |         +--- com.google.guava:guava:30.1-android (*)
|    |    |    |    |         |         +--- com.google.errorprone:error_prone_annotations:2.4.0
|    |    |    |    |         |         +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    |         |         \--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.25
|    |    |    |    +--- org.jctools:jctools-core:3.3.0
|    |    |    |    \--- com.alibaba:fastjson:1.2.75 -> 1.2.72
|    |    |    \--- org.aspectj:aspectjweaver:1.9.4 -> 1.9.2
|    |    \--- org.yaml:snakeyaml:1.28 -> 1.23
|    +--- io.netty:netty-all:4.1.65.Final -> 4.1.31.Final
|    +--- com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    +--- org.aspectj:aspectjrt:1.9.2
|    |    \--- org.aspectj:aspectjweaver:1.9.2
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.0
|    |    +--- com.alibaba.csp:sentinel-core:1.8.0
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.0
|    |    \--- com.alibaba.csp:sentinel-core:1.8.0
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2.1.4.RELEASE
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2.1.4.RELEASE
+--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
+--- org.apache.httpcomponents:httpclient:4.5.12 (*)
+--- com.alibaba:fastjson:1.2.72
+--- cn.hutool:hutool-dfa:5.2.1
|    +--- cn.hutool:hutool-core:5.2.1
|    \--- cn.hutool:hutool-json:5.2.1
|         \--- cn.hutool:hutool-core:5.2.1
+--- commons-validator:commons-validator:1.6
|    +--- commons-beanutils:commons-beanutils:1.9.2 -> 1.9.4 (*)
|    +--- commons-digester:commons-digester:1.8.1
|    +--- commons-logging:commons-logging:1.2
|    \--- commons-collections:commons-collections:3.2.2
+--- commons-codec:commons-codec:1.10
+--- io.github.openfeign.form:feign-form:3.8.0 (*)
+--- io.github.openfeign.form:feign-form-spring:3.8.0 (*)
+--- io.github.openfeign:feign-gson:9.5.1
|    +--- io.github.openfeign:feign-core:9.5.1 -> 10.1.0
|    \--- com.google.code.gson:gson:2.5 -> 2.8.5
+--- io.github.openfeign:feign-okhttp -> 10.1.0
|    +--- io.github.openfeign:feign-core:10.1.0
|    \--- com.squareup.okhttp3:okhttp:3.6.0 -> 3.8.1
|         \--- com.squareup.okio:okio:1.13.0
+--- org.projectlombok:lombok:1.18.12
+--- com.ddmc:promocore-client:1.1.4-SNAPSHOT FAILED
+--- project :promoequity-client
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 2.1.0.RELEASE (*)
|    +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|    +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.9.8 (*)
|    +--- javax.ws.rs:jsr311-api:1.1.1
|    +--- org.apache.commons:commons-lang3:3.7 -> 3.8.1
|    \--- com.ddmc:vouchercore-client:1.0.11-RELEASE
|         +--- org.springframework.cloud:spring-cloud-starter-openfeign:2.1.0.RELEASE (*)
|         +--- com.ddmc:core:1.1.1-SNAPSHOT FAILED
|         +--- com.ddmc:utils:1.1.1-SNAPSHOT FAILED
|         +--- org.projectlombok:lombok:1.18.12
|         +--- io.springfox:springfox-swagger2:2.7.0 (*)
|         \--- io.springfox:springfox-swagger-ui:2.7.0 (*)
+--- com.ddmc:gateway-client:1.7.4-SNAPSHOT FAILED
+--- com.ddmc:gateway-starter:1.7.5-SNAPSHOT FAILED
+--- com.ddmc:point-client:1.1.0-SNAPSHOT FAILED
+--- com.ddmc:promo-bi-client:1.0.0-SNAPSHOT FAILED
+--- org.springframework.data:spring-data-redis:2.1.2.RELEASE
|    +--- org.springframework.data:spring-data-keyvalue:2.1.2.RELEASE -> 2.1.4.RELEASE
|    |    +--- org.springframework.data:spring-data-commons:2.1.4.RELEASE
|    |    |    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    |    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.25
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-tx:5.1.4.RELEASE -> 5.1.5.RELEASE (*)
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- org.springframework:spring-tx:5.1.2.RELEASE -> 5.1.5.RELEASE (*)
|    +--- org.springframework:spring-oxm:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-aop:5.1.2.RELEASE -> 5.1.4.RELEASE (*)
|    +--- org.springframework:spring-context-support:5.1.2.RELEASE -> 5.1.4.RELEASE
|    |    +--- org.springframework:spring-beans:5.1.4.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.1.4.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.slf4j:slf4j-api:1.7.25
+--- io.lettuce:lettuce-core:5.1.3.RELEASE
|    +--- io.projectreactor:reactor-core:3.2.3.RELEASE -> 3.2.5.RELEASE (*)
|    +--- io.netty:netty-common:4.1.31.Final
|    +--- io.netty:netty-transport:4.1.31.Final (*)
|    \--- io.netty:netty-handler:4.1.31.Final (*)
+--- org.springframework.boot:spring-boot-starter-test -> 2.1.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE
|    |    \--- org.springframework.boot:spring-boot:2.1.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.1.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-test:2.1.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.1.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0
|    |    +--- net.minidev:json-smart:2.3
|    |    |    \--- net.minidev:accessors-smart:1.2
|    |    |         \--- org.ow2.asm:asm:5.0.4
|    |    \--- org.slf4j:slf4j-api:1.7.25
|    +--- junit:junit:4.12 -> 4.13
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.assertj:assertj-core:3.11.1
|    +--- org.mockito:mockito-core:2.23.4
|    |    +--- net.bytebuddy:byte-buddy:1.9.3 -> 1.10.16
|    |    +--- net.bytebuddy:byte-buddy-agent:1.9.3 -> 1.10.16
|    |    \--- org.objenesis:objenesis:2.6
|    +--- org.hamcrest:hamcrest-core:1.3
|    +--- org.hamcrest:hamcrest-library:1.3
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    +--- org.springframework:spring-test:5.1.4.RELEASE
|    |    \--- org.springframework:spring-core:5.1.4.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.2
\--- junit:junit:4.13 (*)

testRuntimeOnly - Runtime only dependencies for source set 'test'. (n)
No dependencies

(*) - dependencies omitted (listed previously)

(n) - Not resolved (configuration is not meant to be resolved)

A web-based, searchable dependency report is available by adding the --scan option.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 5.0.
Use '--warning-mode all' to show the individual deprecation warnings.
See https://docs.gradle.org/4.9/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 5s
1 actionable task: 1 executed
