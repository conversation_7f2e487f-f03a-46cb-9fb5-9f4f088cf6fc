##权益中心建表语句
#权益表
create table tb_promotion_equity(
                                    `equity_id` bigint unsigned auto_increment comment '主键',
                                    `equity_type` varchar(30) default '' comment'权益类型，例如 COUPON:优惠券，INTEGRATION:余额，DISCOUNT_GOODS:折扣商品',
                                    `equity_name` varchar(50) default'' comment'权益名称',
                                    `biz_from` varchar(50) default'' comment'权益提供方来源',
                                    `biz_key` varchar(50) default'' comment'提供方来源业务标志',
                                    `expire_type` varchar(20) default'' comment'UN_LIMIT:不受限的，LIMIT:受限的',
                                    `expire_time` timestamp not null default current_timestamp comment'过期时间',
                                    `description` varchar(200) not null default '' comment'备注',
                                    `creator` varchar(50) not null default '' comment'创建者',
                                    `create_time` timestamp not null default current_timestamp comment'创建时间',
                                    `update_time` timestamp not null default current_timestamp on update current_timestamp comment'更新时间',
                                    `publish_time` timestamp not null default current_timestamp comment'发布时间',
                                    `status` varchar(15) default'' comment'状态，NEW,PUBLISH,ABONDON',
                                    `props` varchar(1000) default '{}' comment'额外属性',
                                    `valid` tinyint not null default 1 comment'数据是否有效：0-无效，1-有效',
                                    primary key(`equity_id`)
)ENGINE = InnoDB auto_increment = 10000  comment'权益表' ;

#权益模板组件表
create table tb_promotion_equity_structure(
                                              `equity_structure_id` bigint unsigned auto_increment comment'主键',
                                              `equity_type` varchar(30) not null default '' comment'权益类型',
                                              `create_time` timestamp not null default current_timestamp comment'创建时间',
                                              `update_time` timestamp not null default current_timestamp on update current_timestamp comment'更新时间',
                                              `components` text comment'前端组件',
                                              `valid` tinyint not null default 1 comment'数据是否有效：0-无效，1-有效',
                                              primary key(`equity_structure_id`)
)engine = innodb auto_increment = 10000   comment'权益模板组件表';


#权益发放概览



#权益组，
create table tb_promotion_equity_group(
                                          `group_id` bigint unsigned auto_increment comment'主键',
                                          `group_name` varchar(50) not null default '' comment'权益组名称',
                                          `description` varchar(200) not null default '' comment'描述',
                                          `biz_line` varchar(30) not null default '' comment'使用的业务线',
                                          `use_scene` varchar(30) not null default '' comment'使用场景，例如fanpai',
                                          `creator` varchar(50) not null default '' comment'创建者',
                                          `create_time` timestamp not null default current_timestamp comment'创建时间',
                                          `update_time` timestamp not null default current_timestamp on update current_timestamp comment'更新时间',
                                          `publish_time` timestamp not null default current_timestamp comment'发布时间',
                                          `status` varchar(15) not null default '' comment'状态，INIT-初始化，PUBLISH-已发布，ABONDON-已废弃',
                                          `valid` tinyint not null default 1 comment'数据是否有效：0-无效，1-有效',
                                          primary key(`group_id`)
)engine = innodb auto_increment = 10000 default charset ='utf8'  comment'权益组' ;


#权益组-权益
create table tb_promotion_equity_group_item(
                                               `item_id` bigint unsigned auto_increment comment'主键',
                                               `group_id` bigint unsigned not null default 0 comment'权益组ID',
                                               `equity_id` bigint unsigned not null default 0 comment'权益项',
                                               `create_time` timestamp not null default current_timestamp comment'创建时间',
                                               `valid` tinyint not null default 1 comment'数据是否有效：0-无效，1-有效',
                                               primary key (`item_id`)
)engine = innodb auto_increment = 10000 default charset ='utf8' comment'权益组权益配置';

#权益发放统计
create table tb_promotion_equity_send_statistics(
                                                    `id` bigint unsigned auto_increment comment'默认主键',
                                                    `send_biz_from` varchar(50) not null default '' comment'发放来源',
                                                    `equity_id` bigint unsigned not null default 0 comment'权益id',
                                                    `send_count` bigint unsigned not null default 0 comment'发放次数',
                                                    `create_time` timestamp not null default current_timestamp comment'创建时间',
                                                    `update_time` timestamp not null default current_timestamp on update current_timestamp comment'更新时间',
                                                    `valid` tinyint not null default 1 comment'数据是否有效，0-无效，1-有效',
                                                    primary key(`id`),
                                                    index idx_equity_id(`equity_id`),
                                                    index ix_send_biz_from_equity_id(`send_biz_from`,`equity_id`)
)engine = innodb auto_increment = 10000  comment'权益发放统计';



create table tb_promotion_equity_send_command(
                                                 `id` bigint unsigned auto_increment comment'默认主键',
                                                 `command_id` varchar(50) not null default '' comment'业务主键,依据分表键生成',
                                                 `equity_id` bigint unsigned not null default 0 comment'权益id',
                                                 `user_id` varchar(50) not null default '' comment'用户id,分表键',
                                                 `biz_from` varchar(50) not null default '' comment'调用来源',
                                                 `biz_no` varchar(50) not null default '' comment'幂等号',
                                                 `create_time` timestamp not null default current_timestamp comment'创建时间',
                                                 `update_time` timestamp not null default current_timestamp on update current_timestamp comment'更新时间',
                                                 `props` varchar(2000) not null default '{}' comment'权益发放业务参数',
                                                 `extends_info` varchar(500) not null default '{}' comment'权益发放附加参数',
                                                 `status` varchar(15) not null default '' comment'状态，NEW,SUCCESS,FAIL',
                                                 `valid` tinyint not null default 1 comment'数据是否有效，0-无效，1-有效',
                                                 primary key(`id`),
                                                 unique index uq_command_id(`command_id`),
                                                 unique index uq_biz_no_biz_from(`biz_no`,`biz_from`),
                                                 index idx_user_id_equity_id(`user_id`,`equity_id`)
)engine = innodb auto_increment = 10000  comment'权益发放记录';


create table tb_promotion_equity_send_record(
                                                `id` bigint unsigned auto_increment comment'主键',
                                                `record_id` varchar(50) not null default '' comment'业务主键',
                                                `command_id` varchar(50) not null default '' comment'操作id',
                                                `user_id` varchar(50) not null default '' comment'用户id',
                                                `status` varchar(15) not null default '' comment'状态 FAIL,SUCCESS',
                                                `comment` varchar(2000) not null default '' comment'操作备注',
                                                `create_time` timestamp not null default current_timestamp comment'创建时间',
                                                `valid` tinyint not null default 1 comment'数据是否有效：0-无效，1-有效',
                                                primary key(`id`),
                                                unique index uq_record_id(`record_id`),
                                                index idx_command_id(`command_id`)
)engine = innodb auto_increment = 10000  comment'权益操作记录表';