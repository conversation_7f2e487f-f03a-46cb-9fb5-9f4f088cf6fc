app:
  id: promoequity-service
appname: promoequity-service

server:
  servlet:
    context-path: /promoequity-service

apollo:
  bootstrap:
    enabled: true
    namespaces: application, ddmc_promotion.recallcommonspace
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: promoequity-service
  freemarker:
    checkTemplateLocation: false
  cloud:
    sentinel:
      eager: true
      enabled: true
      datasource:
        flowconfig:
          apollo:
            namespace-name: application
            flow-rules-key: sentinel.flowrules
            rule-type: flow

#mybatie-plus
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    # 逻辑删除配置
    db-config:
      # 删除前
      logic-not-delete-value: 1
      # 删除后
      logic-delete-value: 0