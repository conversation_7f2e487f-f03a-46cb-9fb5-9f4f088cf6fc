package com.ddmc.promoequity.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityGroupQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityGroupRepository;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroupItem;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityGroupItemMapper;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityGroupMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Repository
public class TbPromotionEquityGroupRepositoryImpl extends ServiceImpl<PromotionEquityGroupItemMapper,
        TbPromotionEquityGroupItem> implements PromotionEquityGroupRepository {

    @Resource
    private PromotionEquityGroupMapper promotionEquityGroupMapper;

    @Resource
    private PromotionEquityGroupItemMapper promotionEquityGroupItemMapper;

    @Override
    public TbPromotionEquityGroup findById(Serializable id) {
        return promotionEquityGroupMapper.selectById(id);
    }

    @Override
    public PageResult<TbPromotionEquityGroup> findList(TbPromotionEquityGroup param, PageInfo pageInfo) {
        return PromotionEquityGroupRepository.super.findList(param, pageInfo);
    }

    @Override
    public void insertOrUpdate(TbPromotionEquityGroup tbPromotionEquityGroup) {
        if (tbPromotionEquityGroup.getGroupId() != null) {
            promotionEquityGroupMapper.updateById(tbPromotionEquityGroup);
        } else {
            promotionEquityGroupMapper.insert(tbPromotionEquityGroup);
        }
    }

    @Override
    public long updateStatus(Serializable id, BizStatus from, BizStatus to) {
        return promotionEquityGroupMapper.updateState(id, from.name(), to.name());
    }

    @Override
    public PageResult<TbPromotionEquityGroup> queryList(PromotionEquityGroupQuery query, PageInfo pageInfo) {
        Page page = new Page(pageInfo.getIndex(), pageInfo.getSize());
        QueryWrapper<TbPromotionEquityGroup> queryWrapper = new QueryWrapper<>();

        if (!StringUtils.isEmpty(query.getKeywords())) {
            queryWrapper.eq("group_name", query.getKeywords());
        }
        queryWrapper.eq("valid", 1);
        Page<TbPromotionEquityGroup> pageList = promotionEquityGroupMapper.selectPage(page, queryWrapper);
        PageResult<TbPromotionEquityGroup> result = new PageResult<>();
        result.setIndex(page.getCurrent());
        result.setSize(page.getSize());
        result.setCount(page.getTotal());
        result.setTotal(page.getPages());
        if (pageList.getRecords() != null) {
            result.setResult(pageList.getRecords());
        }
        return result;
    }

    @Override
    public List<TbPromotionEquityGroupItem> queryGroupItem(Long groupId) {
        return promotionEquityGroupItemMapper.queryGroupItem(groupId);
    }

    @Override
    public void addEquityItem(Long groupId, List<TbPromotionEquity> equityList) {
        if (groupId == null || CollectionUtils.isEmpty(equityList)) {
            return;
        }
        List<TbPromotionEquityGroupItem> toSave = new ArrayList<>();
        for (TbPromotionEquity tbPromotionEquity : equityList) {
            TbPromotionEquityGroupItem item = new TbPromotionEquityGroupItem();
            item.setEquityId(tbPromotionEquity.getEquityId());
            item.setGroupId(groupId);
            toSave.add(item);
        }
        this.saveBatch(toSave);
    }

    @Override
    public void clearEquityItem(Long groupId) {
        if (groupId == null) {
            return;
        }
        List<TbPromotionEquityGroupItem> equityIdList = this.queryGroupItem(groupId);
        List<TbPromotionEquityGroupItem> toClear = new ArrayList<>();
        for (TbPromotionEquityGroupItem equityGroupItem : equityIdList) {
            TbPromotionEquityGroupItem item = new TbPromotionEquityGroupItem();
            item.setItemId(equityGroupItem.getItemId());
            item.setValid(0);
            toClear.add(item);
        }
        this.updateBatchById(toClear);
    }
}
