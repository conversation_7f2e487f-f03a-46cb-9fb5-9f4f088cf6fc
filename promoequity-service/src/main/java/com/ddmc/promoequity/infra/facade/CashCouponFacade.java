package com.ddmc.promoequity.infra.facade;

import cn.hutool.json.JSONUtil;
import com.csoss.monitor.api.cat.Cat;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.infra.facade.entity.SendCashCouponParam;
import com.ddmc.voucherprod.client.WechatCashTicketClient;
import com.ddmc.voucherprod.dto.wechatcash.request.SendWechatCashTicketRequest;
import com.ddmc.voucherprod.dto.wechatcash.response.SendCashTicketResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 代金券外观层
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Component
@Slf4j
public class CashCouponFacade {

    @Resource
    private WechatCashTicketClient wechatCashTicketClient;

    public ResponseBaseVo<SendCashTicketResponse> sendCashCoupon(SendCashCouponParam sendCashCouponParam) {
        SendWechatCashTicketRequest request = new SendWechatCashTicketRequest();
        request.setRequestNo(sendCashCouponParam.getRequestNo());
        request.setOpenId(sendCashCouponParam.getOpenId());
        request.setUserId(sendCashCouponParam.getUserId());
        request.setTicketId(sendCashCouponParam.getTicketId());
        try {
            log.info("代金券发送-开始发券,param={}", JSONUtil.toJsonStr(request));
            final ResponseBaseVo<SendCashTicketResponse> resp = wechatCashTicketClient.sendCashTicket(request);
            log.info("代金券发送-发券完毕,uid={},ticketId={}", sendCashCouponParam.getUserId(),
                    JSONUtil.toJsonStr(resp.getData()));
            return resp;
        } catch (Exception e) {
            log.error("代金券发送-发券异常,uid={},ticketId={},error={}", sendCashCouponParam.getUserId(),
                    sendCashCouponParam.getTicketId(), e.getMessage());
            throw e;
        }
    }

}
