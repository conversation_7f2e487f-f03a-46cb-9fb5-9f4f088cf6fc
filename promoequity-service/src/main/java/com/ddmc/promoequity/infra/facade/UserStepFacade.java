package com.ddmc.promoequity.infra.facade;

import com.ddmc.preferbase.client.UserStepClient;
import com.ddmc.preferbase.common.ApiResult;
import com.ddmc.preferbase.dto.step.VirtualStepSaveDTO;
import com.ddmc.promoequity.infra.facade.entity.IncreaseVirtualStepsParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ddmc.core.view.compat.ResponseBaseVo;

/**
 * @class: UserStepFacade
 * @date: 2022/12/27 14:15
 * @author: wangfan
 */
@Component
@Slf4j
public class UserStepFacade {

    @Autowired
    private UserStepClient userStepClient;

    public ResponseBaseVo<Void> increaseVirtualSteps(IncreaseVirtualStepsParam param) {

        VirtualStepSaveDTO stepReq = new VirtualStepSaveDTO();
        stepReq.setUid(param.getUid());
        stepReq.setStep(param.getStep());
        stepReq.setOriginId(param.getBizNo());
        stepReq.setStepType(param.getBizFrom());
        try {
            log.info("[虚拟步数] 开始发放,request={}", stepReq);
            ApiResult<Void> result = userStepClient.saveVirtualStep(stepReq);
            log.info("[虚拟步数] 发放完毕,response={}", result);
            return result;
        } catch (Exception e) {
            log.error("[虚拟步数] 发券异常,message={}", e.getMessage(), e);
            throw e;
        }
    }




}
