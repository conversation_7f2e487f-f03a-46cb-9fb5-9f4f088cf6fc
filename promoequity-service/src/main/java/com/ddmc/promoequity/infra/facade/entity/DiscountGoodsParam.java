package com.ddmc.promoequity.infra.facade.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/10/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DiscountGoodsParam {
    private String activityId;
    private String userId;
    private String goodsId;
    private BigDecimal goodsPrice;
    private Long startTime;
    private Long endTime;
    private String stationId;
}
