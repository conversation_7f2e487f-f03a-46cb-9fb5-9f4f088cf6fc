package com.ddmc.promoequity.infra.service;

import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.common.constant.MonitorConstants;
import com.ddmc.promoequity.common.enums.ResultStatusEnums;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.infra.factory.EquitySendCommandFactory;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityMapper;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquitySendStatisticsMapper;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendRecord;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendRecordMapper;
import com.ddmc.promoequity.utils.CatUtils;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Slf4j
public class EquitySendCommandStateServiceImpl {

    @Resource
    private PromotionEquitySendCommandMapper commandMapper;

    @Resource
    private PromotionEquitySendRecordMapper recordMapper;

    @Resource
    private PromotionEquityMapper promotionEquityMapper;

    @Resource
    private EquitySendCommandFactory equitySendCommandFactory;

    @Resource
    private IDGenerator idGenerator;

    @Resource
    private PromotionEquitySendStatisticsMapper promotionEquitySendStatisticsMapper;

    /**
     * EquitySendStatisticsIncrease 统计开关
     */
    @Value("${promoequity.EquitySendStatisticsIncrease.switch:false}")
    private boolean equitySendStatisticsIncreaseSwitch;

    private void checkStart(EquitySendCommandDO command) {
        TbPromotionEquitySendCommand check = commandMapper.queryByUnique(command.getBizFrom(), command.getBizNo(),
                command.getUid());
        Assert.isNull(check, String.format("请求重复了，BizFrom:%s,BizNo:%s,Uid:%s", command.getBizFrom(),
                command.getBizNo(), command.getUid()));
    }

    /**
     * 权益发送开始
     */
    public String start(EquitySendCommandDO command) {
        try {
//            checkStart(command);
            TbPromotionEquitySendCommand tbPromotionEquitySendCommand = createCommand(command);
            commandMapper.insert(tbPromotionEquitySendCommand);
            //回填id
            command.setCommandId(tbPromotionEquitySendCommand.getCommandId());

            return tbPromotionEquitySendCommand.getCommandId();
        } catch (Exception e) {
            log.error("[权益发送-开始] fail,command:{}", command, e);
            throw e;
        }

    }

    /**
     * 权益发送成功
     */
    public void success(String commandId, ExtendInfo extendInfo) {
        try {
            TbPromotionEquitySendCommand command = commandMapper.selectByCommandId(commandId);
            if (command != null) {

                commandMapper.updateExtendsByCommandId(command.getCommandId(), ResultStatusEnums.SUCCESS.name(),
                        JSONUtil.toJsonStr(extendInfo));
                //更新record
                TbPromotionEquitySendRecord successRecord = createRecord(command, extendInfo,
                        ResultStatusEnums.SUCCESS.name(), "success");
                recordMapper.insert(successRecord);
            }
        } catch (Exception e) {
            log.error("[权益发送-成功响应] fail,commandId:{}", commandId, e);
        }
    }

    /**
     * 发放权益统计。得加一个 lock 锁
     *
     * @param commandId 命令 ID
     */
    public void increaseSendCount(String commandId) {
        try {
            // Apollo 开关控制 - 放在最开始，参数校验前面
            if (equitySendStatisticsIncreaseSwitch) {
                CatUtils.logEvent(MonitorConstants.EquitySendStatisticsIncrease.SWITCH_ON, "switch_on", "success", commandId);
                log.info("EquitySendStatisticsIncrease switch is on, skip increaseSendCount logic, commandId: {}", commandId);
                return;
            }

            if (commandId == null) {
                return;
            }

            // 新老逻辑打点
            CatUtils.logEvent(MonitorConstants.EquitySendStatisticsIncrease.OLD_LOGIC, "old_logic", "success", commandId);
            EquitySendCommandDO equitySendCommandDO =
                    equitySendCommandFactory.createCommandDO(commandMapper.selectByCommandId(commandId));
            BigDecimal bizCount = null;
            if (equitySendCommandDO.getExtendInfo() != null && equitySendCommandDO.getExtendInfo().getBizCount() != null) {
                bizCount = equitySendCommandDO.getExtendInfo().getBizCount();
            } else {
                bizCount = new BigDecimal(0);
            }
            TbPromotionEquitySendStatistics tbPromotionEquitySendStatistics =
                    promotionEquitySendStatisticsMapper.queryByBizKey(equitySendCommandDO.getPropsInfo().getActivityId(),
                            equitySendCommandDO.getPromotionEquityDO().getEquityId());
            CatUtils.logEvent("EquitySendStatistic", "queryByBizKey");
            if (tbPromotionEquitySendStatistics == null) {
                promotionEquityMapper.lock(equitySendCommandDO.getPromotionEquityDO().getEquityId());
                tbPromotionEquitySendStatistics =
                        promotionEquitySendStatisticsMapper.queryByBizKey(equitySendCommandDO.getPropsInfo().getActivityId(),
                                equitySendCommandDO.getPromotionEquityDO().getEquityId());
                CatUtils.logEvent("EquitySendStatistic", "queryByBizKey");
                if (tbPromotionEquitySendStatistics == null) {
                    TbPromotionEquitySendStatistics toSave = new TbPromotionEquitySendStatistics();
                    toSave.setEquityId(equitySendCommandDO.getPromotionEquityDO().getEquityId());
                    toSave.setEquityType(equitySendCommandDO.getPromotionEquityDO().getEquityType().name());
                    toSave.setSendBizFrom(equitySendCommandDO.getBizFrom());
                    toSave.setBizCount(bizCount);
                    toSave.setSendBizKey(equitySendCommandDO.getPropsInfo().getActivityId());
                    toSave.setSendCount(1L);
                    promotionEquitySendStatisticsMapper.insert(toSave);
                    CatUtils.logEvent("EquitySendStatistic", "insert");
                } else {
                    BigDecimal totalBizCount = tbPromotionEquitySendStatistics.getBizCount() == null ?
                            new BigDecimal(0) :
                            tbPromotionEquitySendStatistics.getBizCount();
                    totalBizCount = totalBizCount.add(bizCount);
                    promotionEquitySendStatisticsMapper.increaseSendCount(tbPromotionEquitySendStatistics.getId(), 1L
                            , totalBizCount);
                    CatUtils.logEvent("EquitySendStatistic", "increaseSendCount");
                }
            } else {
                //直接次数加一
                BigDecimal totalBizCount = tbPromotionEquitySendStatistics.getBizCount() == null ? new BigDecimal(0) :
                        tbPromotionEquitySendStatistics.getBizCount();
                totalBizCount = totalBizCount.add(bizCount);
                promotionEquitySendStatisticsMapper.increaseSendCount(tbPromotionEquitySendStatistics.getId(), 1L,
                        totalBizCount);
                CatUtils.logEvent("EquitySendStatistic", "increaseSendCount");
            }
        } catch (Exception e) {
            log.error("increaseSendCount-error,request:{}", commandId, e);
        }
    }


    /**
     * 权益发送失败
     */
    public void fail(String commandId, String msg) {
        try {
            TbPromotionEquitySendCommand command = commandMapper.selectByCommandId(commandId);
            if (command != null) {
                commandMapper.updateByCommandId(commandId, ResultStatusEnums.FAIL.name());
                TbPromotionEquitySendRecord failRecord = createRecord(commandId, command.getUserId(),
                        ResultStatusEnums.FAIL.name(), msg);
                recordMapper.insert(failRecord);
            }
        } catch (Exception e) {
            log.error("[权益发送-失败响应] fail,commandId:{},msg:{}", commandId, msg, e);
        }
    }

    public EquitySendCommandDO detail(String commandId) {
        if (commandId == null) {
            return null;
        }
        TbPromotionEquitySendCommand commandEntity = commandMapper.selectByCommandId(commandId);
        if (commandEntity == null) {
            return null;
        }
        EquitySendCommandDO equitySendCommandDO = equitySendCommandFactory.createCommandDO(commandEntity);
        return equitySendCommandDO;
    }


    private TbPromotionEquitySendRecord createRecord(String commandId, String uid, String status, String msg) {
        TbPromotionEquitySendRecord tbPromotionEquitySendRecord = new TbPromotionEquitySendRecord();
        tbPromotionEquitySendRecord.setRecordId(idGenerator.recordId(uid));
        tbPromotionEquitySendRecord.setCommandId(commandId);
        if (!StringUtils.isEmpty(msg) && msg.length() > 2000) {
            msg = msg.substring(0, 2000);
        }
        tbPromotionEquitySendRecord.setComment(msg);
        tbPromotionEquitySendRecord.setStatus(status);
        return tbPromotionEquitySendRecord;
    }

    private TbPromotionEquitySendRecord createRecord(TbPromotionEquitySendCommand command, ExtendInfo extendInfo,
                                                     String status, String msg) {
        TbPromotionEquitySendRecord tbPromotionEquitySendRecord = new TbPromotionEquitySendRecord();
        tbPromotionEquitySendRecord.setRecordId(idGenerator.recordId(command.getUserId()));
        tbPromotionEquitySendRecord.setCommandId(command.getCommandId());
        if (!StringUtils.isEmpty(msg) && msg.length() > 2000) {
            msg = msg.substring(0, 2000);
        }
        tbPromotionEquitySendRecord.setComment(msg);
        tbPromotionEquitySendRecord.setStatus(status);
        tbPromotionEquitySendRecord.setUserId(command.getUserId());
        tbPromotionEquitySendRecord.setEquityId(command.getEquityId());
        tbPromotionEquitySendRecord.setEquityType(command.getEquityType());
        tbPromotionEquitySendRecord.setBizFrom(command.getBizFrom());
        tbPromotionEquitySendRecord.setBizKey("");
        if (!StringUtils.isEmpty(command.getProps())) {
            PropsInfo propsInfo = JSONUtil.toBean( command.getProps(),PropsInfo.class);
            tbPromotionEquitySendRecord.setBizKey(propsInfo.getActivityId());
        }
        tbPromotionEquitySendRecord.setBizCount(extendInfo.getBizCount());
        return tbPromotionEquitySendRecord;
    }

    private TbPromotionEquitySendCommand createCommand(EquitySendCommandDO command) {
        TbPromotionEquitySendCommand newCommand = new TbPromotionEquitySendCommand();
        newCommand.setCommandId(idGenerator.commandId(command.getUid()));
        newCommand.setEquityId(command.getPromotionEquityDO().getEquityId());
        newCommand.setUserId(command.getUid());
        newCommand.setBizFrom(command.getBizFrom());
        newCommand.setEquityType(command.getPromotionEquityDO().getEquityType().name());
        newCommand.setBizNo(command.getBizNo());
        newCommand.setStatus(ResultStatusEnums.INIT.name());
        if (command.getPropsInfo() != null) {
            newCommand.setProps(JSONUtil.toJsonStr(command.getPropsInfo()));
        }
        if (command.getExtendInfo() != null) {
            newCommand.setExtendsInfo(JSONUtil.toJsonStr(command.getExtendInfo()));
        }
        return newCommand;
    }
}
