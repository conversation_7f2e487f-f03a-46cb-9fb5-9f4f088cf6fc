package com.ddmc.promoequity.infra.facade;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.common.util.HttpUtil;
import com.ddmc.promoequity.common.util.SignUtil;
import com.ddmc.promoequity.domain.ablity.entity.BalanceResult;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.infra.facade.entity.SendAutoBalanceParam;
import com.ddmc.promoequity.infra.facade.entity.SendBalanceParam;
import com.ddmc.trade.balance.client.BalanceApiClient;
import com.ddmc.trade.balance.dto.req.AutoBalanceRechargeReq;
import com.ddmc.trade.balance.dto.req.BalanceRechargeReq;
import com.ddmc.trade.balance.dto.req.CardConfigListReq;
import com.ddmc.trade.balance.dto.resp.AutoBalanceRechargeResp;
import com.ddmc.trade.balance.dto.resp.BalanceRechargeResp;
import com.ddmc.trade.balance.dto.resp.CardConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Component
@Slf4j
public class BalanceClientFacade {

    @Value("${http.invoke.baseUrl}")
    private String baseHttpUrl;

    @Value("${http.invoke.balanceUrl}")
    private String balanceUrl;
    @Value("${http.invoke.balanceQueryUrl}")
    private String balanceQueryUrl;

    @Value("${http.invoke.balanceChannelId}")
    private String balanceChannelId;
    @Value("${http.invoke.balance.appSecret}")
    private String appSecret;
    @Value("${http.invoke.balance.appId}")
    private String appId;


    @Value("${balance.queryList.api.toggle.enable:false}")
    private Boolean queryListApiToggleEnable;

    @Resource
    private BalanceApiClient balanceApiClient;


    /**
     * 发送余额
     *
     * @param command
     * @return
     */
    public String sendBalance(EquitySendCommandDO command) {
        //构建参数
        TreeMap<String, Object> param = buildSendTreeMap(command);
        String url = baseHttpUrl + balanceUrl;
        log.info("开始发送余额权益, url:{} , req:{}", url, JSONUtil.toJsonStr(param));
        //调用
        String httpPostResult = HttpUtil.doPostByAppForm(url, param);
        if (StringUtils.isBlank(httpPostResult)) {
            log.error("调用发送余额接口返回为空, req:{}", JSONUtil.toJsonStr(param));
            return null;
        }
        return httpPostResult;
    }

    private TreeMap<String, Object> buildSendTreeMap(EquitySendCommandDO command) {
        TreeMap<String, Object> param = new TreeMap<>();
        param.put("appId", appId);
        param.put("channel_id", balanceChannelId);
        param.put("card_id", command.getPropsInfo().getBalanceInfo().getCardId());
        param.put("serial_num", command.getBizNo());
        param.put("uid", command.getUid());
        param.put("recharge_source", 1);
        param.put("token", SignUtil.signForBalance(appSecret, param));
        return param;
    }

    private TreeMap<String, Object> buildQueryTreeMap() {
        TreeMap<String, Object> param = new TreeMap<>();
        param.put("appId", appId);
        param.put("channel_id", balanceChannelId);
        param.put("token", SignUtil.signForBalance(appSecret, param));
        return param;
    }


    public List<BalanceResult> queryBalanceList() {
        if (queryListApiToggleEnable) {
            return queryBalanceList(null);
        }
        //构建参数
        TreeMap<String, Object> param = buildQueryTreeMap();
        String url = baseHttpUrl + balanceQueryUrl;
        log.info("开始查询余额权益, url:{} , req:{}", url, JSONUtil.toJsonStr(param));
        //调用
        String httpPostResult = HttpUtil.doPostByAppForm(url, param);
        String errorMsg = "";
        if (StringUtils.isBlank(httpPostResult)) {
            errorMsg = String.format("调用查询余额接口返回为空, req: %s", JSONUtil.toJsonStr(param));
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        JSONObject json = JSONUtil.parseObj(httpPostResult);
        if (json != null && Boolean.TRUE.equals(json.getBool("success"))) {
            JSONArray dataArray = json.getJSONArray("data");
            if (!CollectionUtils.isEmpty(dataArray)) {
                return JSONUtil.toList(dataArray, BalanceResult.class);
            }
        }
        errorMsg = String.format("查询接口返回错误，res: %s", httpPostResult);
        log.error(errorMsg);
        throw new IllegalArgumentException(errorMsg);
    }

    /**
     * 发送余额-new
     *
     * @param param
     * @return
     */
    public BalanceRechargeResp sendBalance(SendBalanceParam param) {
        log.info("={}", JSONUtil.toJsonStr(param));
        BalanceRechargeReq req = new BalanceRechargeReq();
        req.setUid(param.getUid());
        req.setCardId(param.getCardId());
        req.setChannelId(balanceChannelId);
        req.setRechargeSource(1);
        req.setSerialNum(param.getSerialNum());
        try {
            ResponseBaseVo<BalanceRechargeResp> recharge = balanceApiClient.balanceRecharge(req);
            if (recharge == null) {
                throw ResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("余额接口返回数据为空");
            }
            if (!recharge.isSuccess()) {
                throw ResultCodeEnum.SYSTEM_EXCEPTION.toServiceException(recharge.getMsg());
            }
            return recharge.getData();
        } catch (Throwable e) {
            log.error("BalanceClientFacade::sendBalance exception,req ={}", JSONUtil.toJsonStr(param));
            throw e;
        }
    }


    /**
     * 查询余额列表-new
     *
     * @param
     * @return
     */
    public List<BalanceResult> queryBalanceList(String channelId) {
        CardConfigListReq req = new CardConfigListReq();
        channelId = StringUtils.isEmpty(channelId) ? balanceChannelId : channelId;
        req.setChannelId(channelId);
        try {
            ResponseBaseVo<List<CardConfigResp>> resp = balanceApiClient.cardConfigList(req);
            if (resp == null || !resp.isSuccess()) {
                throw ResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("查询余额列表接口返回数据异常");
            }
            return convert(resp.getData());
        } catch (Throwable e) {
            log.error("BalanceClientFacade::sendBalance exception,req ={}", channelId);
            throw e;
        }
    }

    private List<BalanceResult> convert(List<CardConfigResp> cardList) {
        if (CollectionUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }
        return cardList.stream().map(temp -> BalanceResult.builder()
                ._id(temp.getId())
                .name(temp.getName())
                .money(StringUtils.isBlank(temp.getMoney()) ? BigDecimal.ZERO : new BigDecimal(temp.getMoney()))
                .build()
        ).collect(Collectors.toList());
    }


    /**
     * 发送余额-新接口不带card id
     *
     * @param param
     * @return
     */
    public AutoBalanceRechargeResp sendAutoBalance(SendAutoBalanceParam param) {
        if (param == null) {
            return null;
        }
        AutoBalanceRechargeReq req = new AutoBalanceRechargeReq();
        req.setBizScene(param.getBizScene());
        req.setUid(param.getUid());
        req.setSerialNum(param.getSerialNum());
        req.setRechargeMoney(param.getRechargeMoney());
        if(param.getSmsInfo() != null){
            req.setSmsId(param.getSmsInfo().getSmsId());
            req.setSmsPara(param.getSmsInfo().getSmsPara());
            req.setSignature(param.getSmsInfo().getSignId());
        }
        try {
            final ResponseBaseVo<AutoBalanceRechargeResp> autoBalanceRecharge = balanceApiClient.autoBalanceRecharge(req);
            log.info("sendAutoBalance,param:{},{}", JSONUtil.toJsonStr(req), com.alibaba.fastjson.JSONObject.toJSONString(autoBalanceRecharge));
            if (autoBalanceRecharge == null) {
                throw ResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("余额-自定义充值接口返回数据为空");
            }
            if (!autoBalanceRecharge.isSuccess()) {
                throw ResultCodeEnum.SYSTEM_EXCEPTION.toServiceException(autoBalanceRecharge.getMsg());
            }
            return autoBalanceRecharge.getData();
        } catch (Throwable e) {
            log.error("BalanceClientFacade::sendAutoBalance exception,req ={}", JSONUtil.toJsonStr(param), e);
            throw e;
        }
    }


}
