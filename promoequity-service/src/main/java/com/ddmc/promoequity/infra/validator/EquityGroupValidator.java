package com.ddmc.promoequity.infra.validator;

import cn.hutool.core.lang.Assert;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityGroupRepository;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class EquityGroupValidator {
    @Resource
    private PromotionEquityRepository promotionEquityRepository;

    @Resource
    private PromotionEquityGroupRepository promotionEquityGroupRepository;

    public void validatorCreate(PromotionEquityGroupDO group) {
        Assert.notNull(group, "权益组不能为空");
        Assert.notEmpty(group.getGroupName(), "权益组名称不能为空");
        Assert.isTrue(group.getGroupName().length() <= 50, "权益组名称不能超过50个字");
        if (!StringUtils.isEmpty(group.getDescription())) {
            Assert.isTrue(group.getDescription().length() <= 200, "描述不能超过200个字");
        }
        Assert.notNull(group.getBizLine(), "适用的业务线不能为空");
        Assert.notNull(group.getUseScene(), "适用的场景不能为空");
        Assert.notEmpty(group.getEquityList(), "内置权益不能为空");
        group.getEquityList().forEach(equity -> {
            if (equity.getEquityId() == null) {
                throw new IllegalArgumentException("权益配置中的权益id不能为空");
            }
            TbPromotionEquity promotionEquity = promotionEquityRepository.findById(equity.getEquityId());
            if (promotionEquity == null) {
                throw new IllegalArgumentException(String.format("权益id：%s 不存在", equity.getEquityId()));
            }
            if (!BizStatus.PUBLISH.name().equals(promotionEquity.getStatus())) {
                throw new IllegalStateException(String.format("权益：%s 不是发布状态，不可用", equity.getEquityId()));
            }
        });
    }

    public void validatorUpdate(PromotionEquityGroupDO groupDO) {
        Assert.notNull(groupDO, "权益组不能为空");
        Assert.notNull(groupDO.getGroupId(), "权益组id不能为空");
        if (!StringUtils.isEmpty(groupDO.getGroupName())) {
            Assert.isTrue(groupDO.getGroupName().length() <= 50, "权益组名称不能超过50个字");
        }
        if (!StringUtils.isEmpty(groupDO.getDescription())) {
            Assert.isTrue(groupDO.getDescription().length() <= 200, "描述不能超过200个字");
        }
        TbPromotionEquityGroup group = promotionEquityGroupRepository.findById(groupDO.getGroupId());
        Assert.notNull(group, "权益不存在");

        Assert.isTrue(Arrays.asList(BizStatus.NEW.name()).contains(group.getStatus()), "只有新建状态的权益组能够修改");

        if (!CollectionUtils.isEmpty(groupDO.getEquityList())) {
            groupDO.getEquityList().forEach(equity -> {
                if (equity.getEquityId() == null) {
                    throw new IllegalArgumentException("权益配置中的权益id不能为空");
                }
                TbPromotionEquity promotionEquity = promotionEquityRepository.findById(equity.getEquityId());
                if (promotionEquity == null) {
                    throw new IllegalArgumentException(String.format("权益id：%s 不存在", equity.getEquityId()));
                }
                if (!BizStatus.PUBLISH.name().equals(promotionEquity.getStatus())) {
                    throw new IllegalStateException(String.format("权益：%s 不是发布状态，不可用", equity.getEquityId()));
                }
            });
        }

    }
}
