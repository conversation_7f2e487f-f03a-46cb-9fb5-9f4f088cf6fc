package com.ddmc.promoequity.infra.factory;

import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.infra.cache.EquityCacheManager;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class EquitySendCommandFactory {

    @Resource
    private EquityCacheManager equityCacheManager;
    @Resource
    private PromotionEquityFactory promotionEquityFactory;

    public EquitySendCommandDO createCommandDO(TbPromotionEquitySendCommand commandEntity) {
        if (commandEntity == null) {
            return null;
        }
        EquitySendCommandDO equitySendCommandDO = new EquitySendCommandDO();
        equitySendCommandDO.setId(commandEntity.getId());
        equitySendCommandDO.setCommandId(commandEntity.getCommandId());
        equitySendCommandDO.setBizFrom(commandEntity.getBizFrom());
        equitySendCommandDO.setBizNo(commandEntity.getBizNo());
        equitySendCommandDO.setUid(commandEntity.getUserId());
        equitySendCommandDO.setStatus(commandEntity.getStatus());
        if (!StringUtils.isEmpty(commandEntity.getProps())) {
            equitySendCommandDO.setPropsInfo(JSONUtil.toBean( commandEntity.getProps(),PropsInfo.class));
        }
        if (!StringUtils.isEmpty(commandEntity.getExtendsInfo())) {
            equitySendCommandDO.setExtendInfo(JSONUtil.toBean( commandEntity.getExtendsInfo(),ExtendInfo.class));
        }
        if (commandEntity.getEquityId() != null) {
            TbPromotionEquity tbPromotionEquity = equityCacheManager.getEquity(commandEntity.getEquityId());
            equitySendCommandDO.setPromotionEquityDO(promotionEquityFactory.createEquity(tbPromotionEquity));
        }
        return equitySendCommandDO;
    }
}
