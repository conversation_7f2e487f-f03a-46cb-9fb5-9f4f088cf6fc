package com.ddmc.promoequity.infra.factory;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.enums.BizFromEnums;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.enums.ExpireTypeEnums;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.utils.json.JsonUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class PromotionEquityFactory {

    public PromotionEquityDO createEquity(TbPromotionEquity tbPromotionEquity) {
        if (tbPromotionEquity == null) {
            return null;
        }
        PromotionEquityDO equityDO = PromotionEquityDO.builder()
                .equityId(tbPromotionEquity.getEquityId())
                .equityType(EquityTypeEnums.get(tbPromotionEquity.getEquityType()))
                .equityName(tbPromotionEquity.getEquityName())
                .bizFrom(BizFromEnums.get(tbPromotionEquity.getBizFrom()))
                .bizKey(tbPromotionEquity.getBizKey())
                .expireType(ExpireTypeEnums.get(tbPromotionEquity.getExpireType()))
                .expireTime(tbPromotionEquity.getExpireTime())
                .description(tbPromotionEquity.getDescription())
                .creator(tbPromotionEquity.getCreator())
                .publishTime(tbPromotionEquity.getPublishTime())
                .createTime(tbPromotionEquity.getCreateTime())
                .updateTime(tbPromotionEquity.getUpdateTime())
                .status(BizStatus.get(tbPromotionEquity.getStatus()))
                .build();
        if (!StringUtils.isEmpty(tbPromotionEquity.getProps())) {
            equityDO.setPropsInfo(JsonUtil.jsonToBean(tbPromotionEquity.getProps(),
                    PropsInfo.class));
        }
        return equityDO;
    }

    public List<PromotionEquityDO> createEquitys(List<TbPromotionEquity> tbPromotionEquitys) {
        if (CollectionUtils.isEmpty(tbPromotionEquitys)) {
            return Collections.EMPTY_LIST;
        }
        return tbPromotionEquitys.stream().map(o -> createEquity(o)).collect(Collectors.toList());
    }
}
