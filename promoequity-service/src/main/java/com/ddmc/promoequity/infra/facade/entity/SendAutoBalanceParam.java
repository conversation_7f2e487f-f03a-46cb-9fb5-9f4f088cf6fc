package com.ddmc.promoequity.infra.facade.entity;

import com.ddmc.promoequity.vo.SmsInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 余额发放-自动发放余额接口
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendAutoBalanceParam {

    /**
     * 业务标识
     */
    private String activityId;

    /**
     * 幂等号
     */
    private String serialNum;

    /**
     * 充值用户
     */
    private String uid;

    /**
     * 充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 业务场景
     */
    private String bizScene;


    /**
     * 短信内容
     */
    private SmsInfo smsInfo;

}

