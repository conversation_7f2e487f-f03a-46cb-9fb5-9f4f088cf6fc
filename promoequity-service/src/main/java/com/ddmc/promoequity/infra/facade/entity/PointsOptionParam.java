package com.ddmc.promoequity.infra.facade.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointsOptionParam {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 积分数
     */
    private Integer point;
    /**
     * 描述
     */
    private String description;

    /**
     * 唯一（幂等）
     */
    private String requestId;
    /**
     * 积分变动类型
     */
    private Integer type;
    /**
     * 订单号
     */
    private String orderNumber;
}
