package com.ddmc.promoequity.infra.facade;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promocore.client.base.ActivityCountClientApi;
import com.ddmc.promocore.client.base.request.lottery.CountIncreaseRequest;
import com.ddmc.promoequity.infra.facade.entity.IncreaseActivityTimeParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
@Slf4j
public class PromotionActivityFacade {
    @Resource
    private ActivityCountClientApi activityCountClientApi;


    public ResponseBaseVo increaseActivityTime(IncreaseActivityTimeParam increaseActivityTimeParam) {
        try {
            if (StringUtils.isEmpty(increaseActivityTimeParam.getActivityId())
                    || StringUtils.isEmpty(increaseActivityTimeParam.getUserId())) {
                return ResponseBaseVo.fail(500, "参数缺失");
            }
            CountIncreaseRequest request = new CountIncreaseRequest();
            request.setCount(increaseActivityTimeParam.getCount());
            request.setActivityId(increaseActivityTimeParam.getActivityId());
            request.setUserId(increaseActivityTimeParam.getUserId());
            request.setBizNo(increaseActivityTimeParam.getBizNo());
            request.setBizFrom(increaseActivityTimeParam.getBizFrom());
            ResponseBaseVo<Integer> result = activityCountClientApi.increase(request);
            if (!result.isSuccess()) {
                log.error("[新增营销活动次数] 失败，request:{},response:{}",
                        increaseActivityTimeParam, result);

            }
            return result;

        } catch (Exception e) {
            log.error("[新增营销活动次数] 异常", e);
            throw e;
        }
    }
}
