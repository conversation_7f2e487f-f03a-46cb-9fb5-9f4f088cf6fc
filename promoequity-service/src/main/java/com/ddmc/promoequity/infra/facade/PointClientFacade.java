package com.ddmc.promoequity.infra.facade;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.discount.client.ServiceResult.ServiceResultCodeEnum;
import com.ddmc.point.client.PointClient;
import com.ddmc.point.client.request.IncreaseRequestVO;
import com.ddmc.promoequity.infra.facade.entity.PointsOptionParam;
import com.ddmc.userpoint.api.NewPointClient;
import com.ddmc.userpoint.api.OpPointClient;
import com.ddmc.userpoint.api.request.DecreaseRequestReq;
import com.ddmc.userpoint.api.request.IncreaseRequestReq;
import com.ddmc.userpoint.api.response.PointExchangeVo;
import com.ddmc.userpoint.api.response.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Component
@Slf4j
public class PointClientFacade {
    @Resource
    private PointClient pointClient;
    @Resource
    private NewPointClient newPointClient;
    @Resource
    private OpPointClient opPointClient;

    @Value("${promoequity.facade.point.by.java:true}")
    private Boolean isJava;

    public ResponseBaseVo<Boolean> increasePoint(PointsOptionParam param) {
        if(isJava){
            return increasePointByJava(param);
        }else{
            return increasePointByGo(param);
        }
    }

    private ResponseBaseVo<Boolean> increasePointByJava(PointsOptionParam param) {
        IncreaseRequestReq requestReq = new IncreaseRequestReq();
        requestReq.setUserId(param.getUserId());
        requestReq.setDescription(param.getDescription());
        requestReq.setPoint(param.getPoint());
        requestReq.setRequestId(param.getRequestId());
        requestReq.setFromType(param.getType());
        requestReq.setOrderNumber(param.getOrderNumber());
        requestReq.setRequestTime(System.currentTimeMillis());
        try {
            log.info("[java积分平台发积分] 开始发积分,request={}", requestReq);
            ResponseBaseVo<Boolean> result = newPointClient.increase(requestReq);
            log.info("[java积分平台发积分] 积分发放完毕,response={}", result);
            return result;
        } catch (Exception e) {
            log.error("[java积分平台发积分] 积分发放异常,request={}", requestReq, e);
            throw e;
        }
    }

    private ResponseBaseVo<Boolean> increasePointByGo(PointsOptionParam param) {
        IncreaseRequestVO requestVo = new IncreaseRequestVO();
        requestVo.setUserId(param.getUserId());
        requestVo.setDescription(param.getDescription());
        requestVo.setPoint(param.getPoint());
        requestVo.setRequestId(param.getRequestId());
        requestVo.setFromType(param.getType());
        requestVo.setOrderNumber(param.getOrderNumber());
        requestVo.setRequestTime(System.currentTimeMillis());
        try {
            log.info("[go积分平台发积分] 开始发积分,request={}", requestVo);
            ResponseBaseVo<Boolean> result = pointClient.increase(requestVo);
            log.info("[go积分平台发积分] 积分发放完毕,response={}", result);
            return result;
        } catch (Exception e) {
            log.error("[go积分平台发积分] 积分发放异常,request={}", requestVo, e);
            throw e;
        }
    }

    public ResponseBaseVo<Boolean> decreasePoint(PointsOptionParam param) {
        DecreaseRequestReq requestReq = new DecreaseRequestReq();
        requestReq.setUserId(param.getUserId());
        requestReq.setDescription(param.getDescription());
        requestReq.setPoint(param.getPoint());
        requestReq.setRequestId(param.getRequestId());
        requestReq.setFromType(param.getType());
        requestReq.setOrderNumber(param.getOrderNumber());
        requestReq.setRequestTime(System.currentTimeMillis());
        try {
            log.info("[java积分平台扣减积分] 开始扣减积分,request={}", requestReq);
            ResponseBaseVo<Boolean> result = newPointClient.decrease(requestReq);
            log.info("[java积分平台扣减积分] 积分扣减完毕,response={}", result);
            return result;
        } catch (Exception e) {
            log.error("[java积分平台扣减积分] 积分扣减异常,request={}", requestReq, e);
            throw e;
        }
    }

    public ResponseBaseVo<Integer> getUserTotalPoint(String userId) {
        try {
            log.info("[java积分平台查询积分] 开始查询积分,request={}", userId);
            ResponseBaseVo<Integer> result = newPointClient.getUserTotalPoint(userId);
            log.info("[java积分平台查询积分] 积分查询完毕,response={}", result);
            if (null == result || result.getData() == null) {
                throw ServiceResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("查询用户积分");
            }
            return ResponseBaseVo.ok(result.getData());
        } catch (Exception e) {
            log.error("[java积分平台查询积分] 积分查询异常,request={}", userId, e);
            throw e;
        }
    }

    public ResponseBaseVo<Boolean> awardPoint(PointsOptionParam param) {
        try {
            log.info("[java积分奖励积分] 开始奖励积分,request={}", param);
            String userId = param.getUserId();
            Integer pointNum = param.getPoint();
            String description = param.getDescription();
            String orderNumber = UUID.randomUUID().toString();
            Integer type = param.getType();
            ResultVo<Object> result = opPointClient.awardPoint(userId, type, pointNum, orderNumber, description);
            log.info("[java积分奖励积分] 积分奖励完毕,response={}", result);
            if (null == result || result.getData() == null) {
                throw ServiceResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("奖励用户积分");
            }
           return ResponseBaseVo.ok((Boolean) result.getData());
        } catch (Exception e) {
            log.error("[java积分奖励积分] 积分奖励异常,request={}",  param, e);
            throw e;
        }
    }

    public ResponseBaseVo<Boolean> pointExchange(PointsOptionParam param) {
        try {
            log.info("[java积分兑换积分] 开始兑换积分,request={}",param);
            String userId = param.getUserId();
            Integer pointNum = param.getPoint();
            String description = param.getDescription();
            String orderNumber = param.getOrderNumber();
            Integer type = param.getType();
            ResultVo<PointExchangeVo> result = opPointClient.pointExchange(userId, type, pointNum, description, orderNumber);
            log.info("[java积分兑换积分] 积分兑换完毕,response={}", result);
            //接口失败时候的返回值, 懂得都懂
            //{"success":false,"code":-1,"msg":"无效的兑换类型","data":{"point_num":null}}
            if (null == result || result.getData() == null || !result.getSuccess()) {
                throw ServiceResultCodeEnum.SYSTEM_EXCEPTION.toServiceException("兑换用户积分");
            }
            return ResponseBaseVo.ok(result.getSuccess());
        } catch (Exception e) {
            log.error("[java积分兑换积分] 积分兑换异常,request={}",  param, e);
            throw e;
        }
    }
}
