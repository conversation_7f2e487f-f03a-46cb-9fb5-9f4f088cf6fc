package com.ddmc.promoequity.infra.cache;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ddmc.promoequity.common.constant.MonitorConstants;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.utils.CatUtils;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 权益缓存管理器
 * 基于 Caffeine 提供权益数据缓存
 */
@Slf4j
@Component
public class EquityCacheManager {

    /**
     * 缓存最大容量
     */
    @Value("${equity.cache.maximum-size:5000}")
    private int cacheMaximumSize;

    /**
     * 写入后过期时间（分钟）- 控制数据最大存活时间，保证数据不会过于陈旧
     */
    @Value("${equity.cache.expire-after-write:30}")
    private int expireAfterWriteMinutes;

    /**
     * 访问后过期时间（分钟）- 控制不活跃数据清理，节省内存
     */
    @Value("${equity.cache.expire-after-access:5}")
    private int expireAfterAccessMinutes;

    @Resource
    private PromotionEquityRepository promotionEquityRepository;

    /**
     * 权益数据专用缓存
     */
    private LoadingCache<Long /* equityId */, TbPromotionEquity> equityCache;

    @PostConstruct
    public void init() {
        // 权益数据缓存
        this.equityCache = Caffeine.newBuilder()
                .maximumSize(cacheMaximumSize)  // Apollo 可配置的最大缓存数量
                .expireAfterWrite(expireAfterWriteMinutes, TimeUnit.MINUTES)  // Apollo 可配置的写入后过期时间
                .expireAfterAccess(expireAfterAccessMinutes, TimeUnit.MINUTES) // Apollo 可配置的访问后过期时间
                .build(this::loadEquity);  // 自动加载函数

        log.info("equityCacheManager init success, maximumSize={}, expireAfterWrite={}min, expireAfterAccess={}min",
                cacheMaximumSize, expireAfterWriteMinutes, expireAfterAccessMinutes);
    }

    /**
     * 加载权益信息
     * <p>
     * 当缓存中没有数据时会自动调用此方法
     *
     * @param equityId 权益 ID
     * @return 权益数据
     */
    private TbPromotionEquity loadEquity(Long equityId) {
        try {
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.LOCAL_CACHE_PENETRATION_TO_DB, "loadEquity");
            TbPromotionEquity promotionEquity = promotionEquityRepository.findById(equityId);
            if (promotionEquity == null) {
                log.info("loadEquity promotionEquity is null equityId={}", equityId);
                CatUtils.logEvent(MonitorConstants.EquityCacheManager.LOAD_EQUITY, "equity_is_null");
                return new TbPromotionEquity();
            }
            return promotionEquity;
        } catch (Exception e) {
            log.error("loadEquity exception equityId={}", equityId, e);
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.LOAD_EQUITY, "exception");
            return null;
        }
    }

    /**
     * 获取权益信息
     *
     * @param equityId 权益 ID
     * @return 权益数据
     */
    public TbPromotionEquity getEquity(Long equityId) {
        if (equityId == null || equityId <= 0) {
            log.error("getEquity param error equityId={}", equityId);
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITY_FROM_CACHE, "param_error");
            return null;
        }

        try {
            TbPromotionEquity promotionEquity = equityCache.get(equityId);
            if (BeanUtil.isEmpty(promotionEquity)) {
                log.info("getEquity promotionEquity is null equityId={}", equityId);
                CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITY_FROM_CACHE, "equity_is_null");
                return null;
            }
            return promotionEquity;
        } catch (Exception e) {
            log.error("getEquity exception equityId={}", equityId, e);
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITY_FROM_CACHE, "exception");
            return null;
        }
    }

    /**
     * 批量获取权益信息
     *
     * @param equityIds 权益 ID 列表
     * @return 权益信息列表
     */
    public List<TbPromotionEquity> getEquities(List<Long> equityIds) {
        if (CollectionUtils.isEmpty(equityIds)) {
            log.error("getEquities param error");
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITIES_FROM_CACHE, "param_error");
            return null;
        }

        // 过滤无效 ID 并去重
        List<Long> validIds = equityIds.stream()
                .filter(id -> id != null && id > 0)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validIds)) {
            log.error("getEquities no valid equityIds");
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITIES_FROM_CACHE, "no_valid_equityIds");
            return null;
        }

        try {
            // 获取缓存中已存在的数据
            List<TbPromotionEquity> result = validIds.stream()
                    .map(equityCache::getIfPresent)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 获取已缓存的 ID 列表
            List<Long> cachedIds = result.stream()
                    .map(TbPromotionEquity::getEquityId)
                    .collect(Collectors.toList());

            // 计算未缓存的 ID
            List<Long> uncachedIds = validIds.stream()
                    .filter(id -> !cachedIds.contains(id))
                    .collect(Collectors.toList());

            // 批量查询未缓存的数据并加入缓存
            if (CollectionUtils.isNotEmpty(uncachedIds)) {
                List<TbPromotionEquity> batchResults = promotionEquityRepository.selectByIds(uncachedIds);
                batchResults.forEach(equity -> equityCache.put(equity.getEquityId(), equity));
                result.addAll(batchResults);
                log.debug("getEquities batchResults size={}, cachedIds={}, uncachedIds={}",
                        batchResults.size(), JSON.toJSONString(cachedIds), JSON.toJSONString(uncachedIds));
            }

            // 需要过滤空权益信息
            List<TbPromotionEquity> notNullEquities = result.stream()
                    .filter(BeanUtil::isNotEmpty)
                    .collect(Collectors.toList());
            // 如果存在空的权益信息，打点
            if (CollectionUtils.size(notNullEquities) != CollectionUtils.size(validIds)) {
                log.error("getEquities hasNullEquities notNullEquitiesSize={}, validIdsSize={}, validIds={}",
                        CollectionUtils.size(notNullEquities), CollectionUtils.size(validIds), JSON.toJSONString(validIds));
                CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITIES_FROM_CACHE, "has_null_equities");
                return null;
            }

            return notNullEquities;
        } catch (Exception e) {
            log.error("getEquities exception equityIds={}", JSON.toJSONString(equityIds), e);
            CatUtils.logEvent(MonitorConstants.EquityCacheManager.GET_EQUITIES_FROM_CACHE, "exception");
            return null;
        }
    }
} 