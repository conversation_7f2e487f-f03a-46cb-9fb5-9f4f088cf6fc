package com.ddmc.promoequity.infra.convert;

import com.ddmc.promoequity.domain.ablity.entity.BalanceResult;
import com.ddmc.promoequity.vo.BalanceInfo;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Component
public class BalanceInfoConvert {

    public BalanceInfo resultCovertToInfo(BalanceResult result) {
        if (result != null) {
            BalanceInfo info = new BalanceInfo();
            info.setCardId(result.get_id());
            info.setCardName(result.getName());
            info.setCardPrice(result.getMoney());
            return info;
        }
        return null;
    }

    public List<BalanceInfo> resultCoverToInfoList(List<BalanceResult> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        return resultList.stream().map(o -> resultCovertToInfo(o)).collect(Collectors.toList());
    }
}
