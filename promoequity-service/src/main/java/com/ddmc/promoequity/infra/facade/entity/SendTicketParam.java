package com.ddmc.promoequity.infra.facade.entity;

import com.ddmc.vouchercore.client.request.sub.RiskCheckRequest;
import lombok.Data;

@Data
public class SendTicketParam {
    private String bizFrom;
    private String userId;

    private String ticketId;
    private String ticketPackageId;

    private String type;

    /**
     * 用作幂等
     */
    private String code;

    private String activityId;
    private String prizeId;
    /**
     * 邀请是24 邀邻25  第三方是23, 自动发券是27
     */
    private Integer scene;
    /**
     * 用户优惠券是否已读，控制未读提示和天降等。scene in (12,17,108) 无论 isRead 传值是什么，券平台写死为已读；其他 scene 取传值，为空默认为未读
     */
    private Boolean isRead;
    /**
     * 风控校验
     */
    private RiskCheckRequest riskCheckRequest;

    private Long startTime;
    private Long expireTime;
}
