package com.ddmc.promoequity.infra.facade.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发送余额的
 *
 * <AUTHOR>
 * @date 2022/8/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendBalanceParam {

    private String channelId;

    /**
     * 充值卡标识
     */
    private String cardId;

    /**
     * 充值序列号
     */
    private String serialNum;

    /**
     * 充值来源
     */
    private Integer rechargeSource;

    /**
     * 充值用户
     */
    private String uid;
}
