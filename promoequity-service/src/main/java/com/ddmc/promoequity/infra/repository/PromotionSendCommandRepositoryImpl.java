package com.ddmc.promoequity.infra.repository;

import com.ddmc.promoequity.domain.entity.MorePageResult;
import com.ddmc.promoequity.domain.entity.vo.EquityCommandListQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionSendCommandRepository;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class PromotionSendCommandRepositoryImpl implements PromotionSendCommandRepository {

    @Resource
    private PromotionEquitySendCommandMapper commandMapper;

    @Override
    public MorePageResult<TbPromotionEquitySendCommand> findListByPreKey(EquityCommandListQuery query) {
        if (StringUtils.isEmpty(query.getBizFrom()) || StringUtils.isEmpty(query.getUid())) {
            return MorePageResult.empty();
        }
        List<TbPromotionEquitySendCommand> data = commandMapper.selectListByPreKey(query.getBizFrom(), query.getUid(), query.getSize(), query.getPreCommandId());
        MorePageResult<TbPromotionEquitySendCommand> result = new MorePageResult<>();
        result.setRows(data);
        result.setHasMore(false);
        if (!CollectionUtils.isEmpty(data)) {
            long count = commandMapper.countByPreKey(query.getUid(), query.getBizFrom(), data.get(data.size() - 1).getId());
            result.setHasMore(count > 0);
        }
        return result;
    }
}
