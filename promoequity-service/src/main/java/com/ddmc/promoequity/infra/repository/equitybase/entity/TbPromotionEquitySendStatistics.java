package com.ddmc.promoequity.infra.repository.equitybase.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TbPromotionEquitySendStatistics {
    @TableId
    private Long id;
    private String sendBizFrom;
    private String sendBizKey;
    private Long equityId;
    private String equityType;
    private Long sendCount;
    private Date createTime;
    private Date updateTime;
    private BigDecimal bizCount;
    private int valid;
}
