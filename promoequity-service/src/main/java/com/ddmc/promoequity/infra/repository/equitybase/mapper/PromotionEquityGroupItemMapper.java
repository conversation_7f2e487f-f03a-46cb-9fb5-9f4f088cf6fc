package com.ddmc.promoequity.infra.repository.equitybase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroupItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PromotionEquityGroupItemMapper extends BaseMapper<TbPromotionEquityGroupItem> {
    @Select("select * from tb_promotion_equity_group_item where group_id = #{groupId} and valid = 1")
    List<TbPromotionEquityGroupItem> queryGroupItem(Long groupId);
}
