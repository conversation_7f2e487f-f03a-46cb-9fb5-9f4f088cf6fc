package com.ddmc.promoequity.infra.validator;

import cn.hutool.core.lang.Assert;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.ablity.PromotionEquityStore;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.enums.ExpireTypeEnums;
import com.ddmc.promoequity.infra.cache.EquityCacheManager;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class PromotionEquityValidator {

    @Resource
    private PromotionEquityRepository promotionEquityRepository;
    @Resource
    private EquityCacheManager equityCacheManager;
    @Resource
    private PromotionEquityStore promotionEquityStore;

    @Value("#{'${BIZ_FROM_WHITE_LIST}'.split(',')}")
    private List<String> whiteBizFromList;
    @Value("${WHITE_SWITCH:N}")
    private String whiteListSwitch;

    public void validateCreate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO, "权益信息不能为空");
        Assert.isTrue(promotionEquityDO.getEquityId() == null, "已保存的权益不能重复保存");
        Assert.notNull(promotionEquityDO.getEquityType(), "权益类型不能为空");
        Assert.notNull(promotionEquityDO.getEquityName(), "权益名称不能为空");
        Assert.isTrue(promotionEquityDO.getEquityName().length() <= 256, "权益名称不能超过 256 个字");
        Assert.notNull(promotionEquityDO.getBizFrom(), "权益来源不能为空");
        Assert.notNull(promotionEquityDO.getExpireType(), "失效类型不能为空");
        if (promotionEquityDO.getExpireType() != ExpireTypeEnums.UN_LIMIT) {
            Assert.notNull(promotionEquityDO.getExpireTime(), "失效时间不能为空");
        }

        if (!StringUtils.isEmpty(promotionEquityDO.getDescription())) {
            Assert.isTrue(promotionEquityDO.getDescription().length() <= 200, "备注最长为200字");
        }
        promotionEquityStore.validate(promotionEquityDO);

    }

    public void validateUpdate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO, "权益不能为空");
        Assert.isTrue(promotionEquityDO.getEquityId() != null, "权益ID不能为空");
        if (!StringUtils.isEmpty(promotionEquityDO.getEquityName())) {
            Assert.isTrue(promotionEquityDO.getEquityName().length() <= 256, "权益名称不能超过 256 个字");
        }
        if (!StringUtils.isEmpty(promotionEquityDO.getDescription())) {
            Assert.isTrue(promotionEquityDO.getDescription().length() <= 200, "备注最长为200字");
        }
        TbPromotionEquity tbPromotionEquity = promotionEquityRepository.findById(promotionEquityDO.getEquityId());
        if (tbPromotionEquity == null) {
            throw new IllegalStateException("权益不存在");
        }
        if (!Arrays.asList(BizStatus.NEW.name()).contains(tbPromotionEquity.getStatus())) {
            throw new IllegalStateException("只有未发布状态的权益能够修改");
        }
        promotionEquityStore.validate(promotionEquityDO);
    }

    public void validateSend(EquitySendDO equitySendDO) {
        Assert.notNull(equitySendDO, "发送权益对象为空");
        Assert.notNull(equitySendDO.getEquityId(), "权益id【equityId】不能为空");
        TbPromotionEquity tbPromotionEquity = equityCacheManager.getEquity(equitySendDO.getEquityId());
        if (tbPromotionEquity == null || tbPromotionEquity.getValid() == 0) {
            throw new IllegalStateException("权益不存在，equityId:" + equitySendDO.getEquityId());
        }
        if (!tbPromotionEquity.getStatus().equals(BizStatus.PUBLISH.name())) {
            throw new IllegalStateException("权益状态异常");
        }
        Assert.notBlank(equitySendDO.getUid(), "用户id【uid】不能为空");
        Assert.notBlank(equitySendDO.getBizFrom(), "请求来源【bizFrom】不能为空");
        if ("Y".equals(whiteListSwitch) && !whiteBizFromList.contains(equitySendDO.getBizFrom())) {
            throw new IllegalStateException("非法的请求来源，请联系管理员注册");
        }
        Assert.isTrue(equitySendDO.getBizNo().length() <= 64, "bizNo 长度不能大于 64");
        Assert.notBlank(equitySendDO.getBizKey(), "统计字段【bizKey】 不能为空");
    }
}
