package com.ddmc.promoequity.infra.service;

import com.ddmc.promoequity.infra.repository.equityuser.mapper.IDMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class IDGenerator {

    @Resource
    private IDMapper iDMapper;
//    @Resource
//    private ActivityGroupMapper activityGroupMapper;
//
//    public String commandId(String userId) {
//        if (StringUtils.isEmpty(userId)) {
//            throw new IllegalArgumentException("用户id不能为空");
//        }
//        int hashCode = userId.hashCode();
//        return String.format("C%s%s", activityGroupMapper.getNextVal(), hashCode % 100);
//    }

    public String commandId(String userId){
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        return iDMapper.getNextCommandId(userId);
    }


//    public String recordId(String commandId) {
//        if (StringUtils.isEmpty(commandId)) {
//            throw new IllegalArgumentException("commandId不能为空");
//        }
//        String hashCode = commandId.substring(commandId.length() - 2);
//        return String.format("R%s%s", activityGroupMapper.getNextVal(), hashCode);
//    }

    public String recordId(String userId){
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        return iDMapper.getNextRecordId(userId);

    }

}
