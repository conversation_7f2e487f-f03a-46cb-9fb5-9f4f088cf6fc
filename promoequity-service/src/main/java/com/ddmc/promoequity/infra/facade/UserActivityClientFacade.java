package com.ddmc.promoequity.infra.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.ddmc.activitycore.model.UserProductBenefitModel;
import com.ddmc.activitycore.request.QueryUserActivityRequest;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promo.bi.client.UserActivityClient;
import com.ddmc.promo.bi.dto.ActivityAreaDTO;
import com.ddmc.promo.bi.request.UserActivityRequest;
import com.ddmc.promoequity.infra.facade.entity.DiscountGoodsParam;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

import static com.ddmc.promoequity.infra.facade.constants.DiscountGoodsConstant.DEFAULT_BUY_LIMIT;
import static com.ddmc.promoequity.infra.facade.constants.DiscountGoodsConstant.STATION_LEVEL;

/**
 * <AUTHOR>
 * @Date 2021/10/28
 */
@Component
@Slf4j
public class UserActivityClientFacade {

    @Resource
    private UserActivityClient userActivityClient;


    public ResponseBaseVo<List<UserProductBenefitModel>> createDiscountGoods(DiscountGoodsParam discountGoodsParam) {
        try {
            if (StringUtils.isEmpty(discountGoodsParam.getActivityId())
                    || StringUtils.isEmpty(discountGoodsParam.getUserId())) {
                return ResponseBaseVo.fail(500, "参数缺失");
            }
            UserActivityRequest request = new UserActivityRequest();
            // 设置商品信息
            UserActivityRequest.ActivityProductInfo productInfo = new UserActivityRequest.ActivityProductInfo();
            productInfo.setProductId(discountGoodsParam.getGoodsId());
            productInfo.setSpecialOffer(discountGoodsParam.getGoodsPrice());
            productInfo.setBuyLimit(DEFAULT_BUY_LIMIT);
            request.setProductInfo(Lists.newArrayList(productInfo));
            // 设置站点信息
            ActivityAreaDTO activityAreaDTO = new ActivityAreaDTO();
            activityAreaDTO.setLevel(STATION_LEVEL);
            activityAreaDTO.setLevelIds(Lists.newArrayList(discountGoodsParam.getStationId()));
            request.setActivityArea(Lists.newArrayList(activityAreaDTO));
            // 活动id
            request.setActivityId(discountGoodsParam.getActivityId());
            // 用户id
            request.setUserId(discountGoodsParam.getUserId());
            request.setStartTime(discountGoodsParam.getStartTime());
            request.setEndTime(discountGoodsParam.getEndTime());
            ResponseBaseVo<List<UserProductBenefitModel>> result = userActivityClient.createUserActivity(request);
            if (!result.isSuccess()) {
                log.error("[折扣商品发放] 失败，request:{},response:{}",
                        discountGoodsParam, result);
            }
            return result;

        } catch (Exception e) {
            log.error("[折扣商品发放] 异常", e);
            throw e;
        }
    }

    public List<UserProductBenefitModel> queryUserActivityByIds(List<Long> userGoodsIds){
        if(CollectionUtil.isEmpty(userGoodsIds)){
            return null;
        }
        QueryUserActivityRequest message = new QueryUserActivityRequest();
        message.setUserCardIds(userGoodsIds);
        log.info("查询用户折扣品信息，req:{}",message);
        ResponseBaseVo<List<UserProductBenefitModel>> responseBaseVo=
                userActivityClient.queryUserActivityByIds(message);
        if(responseBaseVo.isSuccess()){
            return responseBaseVo.getData();
        }
        log.error("查询用户折扣品信息错误，res:{}",responseBaseVo);
        return null;
    }
}
