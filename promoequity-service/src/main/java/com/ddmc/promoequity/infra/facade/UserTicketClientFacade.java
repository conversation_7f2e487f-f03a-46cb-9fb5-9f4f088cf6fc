package com.ddmc.promoequity.infra.facade;

import cn.hutool.json.JSONUtil;
import com.csoss.monitor.sdk.resource.Status;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.couponbase.client.SendCouponClient;
import com.ddmc.couponbase.client.common.util.SignUtil;
import com.ddmc.couponbase.client.request.SendCouponRequest;
import com.ddmc.gateway.bg.client.model.AuthInfo;
import com.ddmc.gateway.bg.client.utils.AuthUtil;
import com.ddmc.promoequity.infra.EquityMonitor;
import com.ddmc.promoequity.infra.facade.entity.ListTicketBillParam;
import com.ddmc.promoequity.infra.facade.entity.QueryTicketListParam;
import com.ddmc.promoequity.infra.facade.entity.SendTicketParam;
import com.ddmc.promoequity.infra.facade.entity.TicketInfoStatistics;
import com.ddmc.vouchercore.admin.TicketAdminApi;
import com.ddmc.vouchercore.admin.TicketBillAdminApi;
import com.ddmc.vouchercore.admin.enums.TicketBillTypeEnum;
import com.ddmc.vouchercore.admin.request.ListTicketBillRequest;
import com.ddmc.vouchercore.admin.utils.VoucherCoreSignUtils;
import com.ddmc.vouchercore.admin.vo.ListSimpleTicketVo;
import com.ddmc.vouchercore.admin.vo.TicketBillVO;
import com.ddmc.vouchercore.client.SendTicketClient;
import com.ddmc.vouchercore.client.TicketClient;
import com.ddmc.vouchercore.client.TicketPackageClient;
import com.ddmc.vouchercore.client.UserTicketClient;
import com.ddmc.vouchercore.client.dto.TicketDTO;
import com.ddmc.vouchercore.client.dto.TicketPackageDTO;
import com.ddmc.vouchercore.client.dto.UserTicketDTO;
import com.ddmc.vouchercore.client.dto.sub.PageInfoDTO;
import com.ddmc.vouchercore.client.request.GetTicketPackageRequest;
import com.ddmc.vouchercore.client.request.GetTicketRequest;
import com.ddmc.vouchercore.client.request.SendTicketPackageSyncRequest;
import com.ddmc.vouchercore.client.request.SendTicketRequest;
import com.ddmc.vouchercore.client.request.UserTicketsRequest;
import com.ddmc.vouchercore.client.response.PageQueryResult;
import com.ddmc.vouchercore.client.response.SendTicketPackageSyncResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserTicketClientFacade {
    public static final int TICKET_STATUS_PUBLISH = 1;
    public static final int TICKET_STATUS_ACTIVE = 2;
    public static final int QUERY_MAX_SIZE = 20;

    public static final String ENGINE_API_SERVICE_NAME = "recallengine-api-service";
    //分类页领券风控场景code
    public static final String CATEGORY_SCENE_CODE = "activity_coupon_classific";

    @Resource
    private SendTicketClient sendTicketClient;

    @Resource
    private TicketBillAdminApi ticketBillAdminApi;

    @Resource
    private TicketClient ticketClient;
    @Resource
    private TicketPackageClient ticketPackageClient;

    @Resource
    private TicketAdminApi ticketAdminApi;

    @Resource
    private UserTicketClient userTicketClient;

    @Autowired
    private SendCouponClient sendCouponClient;

    @Value("${promoequity.facade.sendTicket.refactor.switch:true}")
    private boolean sendTicketRefactorSwitch;

    @Value("${promoequity.facade.sendTicket.switch:true}")
    private boolean sendTicketSwitch;

    @Value("${promoequity.facade.sendTicket.appName:promoequity-service}")
    private String appName;

    @Value("${promoequity.facade.sendTicket.privateKey:EusUy7nh0oAokgjgLpLl}")
    private String privateKey;

    @Value("#{'${promoequity.facade.sendTicket.refactorServiceName:PROMORECALL}'.split(',')}")
    public Set<String> refactorServiceNameSet = new HashSet<>();

    @Value("#{'${promoequity.facade.sendTicket.sendTicketWithRiskCheckScene:115}'.split(',')}")
    public Set<Integer> sendTicketWithRiskCheckSceneSet = new HashSet<>();

    @Value("${recall.engine.risk.switch:true}")
    public boolean categoryRiskSwitch;


    public ResponseBaseVo sendTicket(SendTicketParam sendTicketParam) {
        try {
            EquityMonitor.logEvent("sendTicket",sendTicketParam.getBizFrom() + "_" + sendTicketParam.getScene(), Status.SUCCESS, sendTicketParam.getUserId());
        }catch (Exception e){
            log.error("Monitor.logEvent exception:",e);
        }
        // scene = 10 召回发券
        if(sendTicketSwitch && "RECALLENGINE-SERVICE".equals(sendTicketParam.getBizFrom()) && Objects.equals(sendTicketParam.getScene(),10)){
            return doSendTicketRefactor(sendTicketParam);
        }
        if (sendTicketRefactorSwitch && refactorServiceNameSet.contains(sendTicketParam.getBizFrom())) {
            //couponbase发券
            return doSendTicketRefactor(sendTicketParam);
        }
        // scene = 115，crm 分类页领券
        if (sendTicketWithRiskCheckSceneSet.contains(sendTicketParam.getScene())) {
            if (ENGINE_API_SERVICE_NAME.equals(sendTicketParam.getBizFrom()) && sendTicketParam.getRiskCheckRequest() != null && CATEGORY_SCENE_CODE.equals(sendTicketParam.getRiskCheckRequest().getScene_code()) && categoryRiskSwitch) {
                return doSendTicketWithoutRisk(sendTicketParam);
            }
            //vouchercore风控发券
            return doSendTicketWithRiskCheck(sendTicketParam);
        }
        // 115-crm 分类页领券；116-crm 天降发券；119-crm 买返活动领券；
        //vouchercore同步发券
        return doSendTicket(sendTicketParam);
    }

    private ResponseBaseVo doSendTicketWithRiskCheck(SendTicketParam sendTicketParam) {
        SendTicketRequest request = new SendTicketRequest();
        request.setTicket_id(sendTicketParam.getTicketId());
        request.setUid(sendTicketParam.getUserId());
        request.setType(sendTicketParam.getType());
        request.setExternal_unique_id(sendTicketParam.getCode());
        request.setScene(sendTicketParam.getScene());
        request.setActivity(sendTicketParam.getActivityId());
        request.setPrize(sendTicketParam.getPrizeId());
        request.setRiskCheckRequest(sendTicketParam.getRiskCheckRequest());
        try {
            log.info("[券平台风控发券] 开始发券,request={}", sendTicketParam);
            ResponseBaseVo result = sendTicketClient.sendTicketSyncWithRiskCheck(request);
            log.info("[券平台风控发券] 发券完毕,response={}", result);
            return result;
        } catch (Exception e) {
            log.error("[券平台风控发券] 发券异常,request={}", sendTicketParam, e);
            throw e;
        }
    }

    private ResponseBaseVo doSendTicketWithoutRisk(SendTicketParam sendTicketParam) {
        SendTicketRequest request = new SendTicketRequest();
        request.setTicket_id(sendTicketParam.getTicketId());
        request.setUid(sendTicketParam.getUserId());
        request.setType(sendTicketParam.getType());
        request.setExternal_unique_id(sendTicketParam.getCode());
        request.setScene(sendTicketParam.getScene());
        request.setActivity(sendTicketParam.getActivityId());
        request.setPrize(sendTicketParam.getPrizeId());
        request.setRiskCheckRequest(sendTicketParam.getRiskCheckRequest());
        try {
//            log.info("[券平台不风控发券] 开始发券,request={}", JsonUtils.toJson(sendTicketParam));
            ResponseBaseVo result = sendTicketClient.sendTicketSyncV2(request);
//            log.info("[券平台不风控发券] 发券完毕,response={}", JsonUtils.toJson(result));
            return result;
        } catch (Exception e) {
            log.error("[券平台不风控发券] 发券异常,request={}", sendTicketParam, e);
            throw e;
        }
    }

    private ResponseBaseVo doSendTicket(SendTicketParam sendTicketParam) {
        SendTicketRequest request = new SendTicketRequest();
        request.setTicket_id(sendTicketParam.getTicketId());
        request.setUid(sendTicketParam.getUserId());
        request.setType(sendTicketParam.getType());
        request.setExternal_unique_id(sendTicketParam.getCode());
        request.setScene(sendTicketParam.getScene());
        request.setActivity(sendTicketParam.getActivityId());
        request.setPrize(sendTicketParam.getPrizeId());
        try {
            log.info("[券平台发券] 开始发券,uid={},ticketId={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketId());
            ResponseBaseVo result = sendTicketClient.sendTicketSync(request);
            log.info("[券平台发券] 发券完毕,uid={},ticketId={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketId());
            return result;
        } catch (Exception e) {
            log.error("[券平台发券] 发券异常,uid={},ticketId={},error={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketId(), e.getMessage());
            throw e;
        }
    }

    private ResponseBaseVo doSendTicketRefactor(SendTicketParam sendTicketParam) {
        SendCouponRequest request = new SendCouponRequest();
        request.setAppid(appName);
        request.setActivity(sendTicketParam.getActivityId());
        request.setPrize(sendTicketParam.getPrizeId());
        request.setTicket_id(sendTicketParam.getTicketId());
        request.setUid(sendTicketParam.getUserId());
        request.setType(sendTicketParam.getType());
        request.setExternal_unique_id(sendTicketParam.getCode());
        request.setScene(sendTicketParam.getScene());
        request.setIsRead(sendTicketParam.getIsRead());
        request.setStart_time(sendTicketParam.getStartTime());
        request.setExpire_time(sendTicketParam.getExpireTime());
        SignUtil.generateRequestParam(request, privateKey);
        try {
            log.info("[couponbase发券] 开始发券 req={}", JSONUtil.toJsonStr(request));
            ResponseBaseVo result = sendCouponClient.sendCouponSync(request);
            log.info("[couponbase发券] 发券完毕 uid={}, ticketId={}, uniqueId={}",
                    sendTicketParam.getUserId(), sendTicketParam.getTicketId(),
                    request.getExternal_unique_id());
            return result;
        } catch (Exception e) {
            log.error("[couponbase发券] 发券异常,uid={},ticketId={},error={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketId(), e.getMessage());
            throw e;
        }
    }

    public ResponseBaseVo<SendTicketPackageSyncResponse> sendTicketPackage(SendTicketParam sendTicketParam) {
        SendTicketPackageSyncRequest request = new SendTicketPackageSyncRequest();
        request.setTicket_package_id(sendTicketParam.getTicketPackageId());
        request.setUid(sendTicketParam.getUserId());
        request.setType(sendTicketParam.getType());
        request.setExternal_unique_id(sendTicketParam.getCode());
        request.setSence(sendTicketParam.getScene());
        request.setActivity(sendTicketParam.getActivityId());
        request.setPrize(sendTicketParam.getPrizeId());
        try {
            log.info("[券平台发券包] 开始发券,uid={},ticketPackageId={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketPackageId());
            ResponseBaseVo<SendTicketPackageSyncResponse> result = sendTicketClient.sendTicketPackageSync(request);
            log.info("[券平台发券包] 发券完毕,uid={},ticketPackageId={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketPackageId());
            return result;
        } catch (Exception e) {
            log.error("[券平台发券包] 发券异常,uid={},ticketPackageId={},error={}",  sendTicketParam.getUserId(),
                    sendTicketParam.getTicketPackageId(),e.getMessage());
            throw e;
        }
    }

    /**
     * 查询券信息
     *
     * @param ticketId
     * @return
     */
    public TicketDTO getTicket(String ticketId) {
        if (ticketId == null) {
            return null;
        }
        try {
            GetTicketRequest ticketRequest = new GetTicketRequest();
            ticketRequest.setTicket_id(ticketId);
            ResponseBaseVo<TicketDTO> responseBaseVo = ticketClient.getTicket(ticketRequest);
            if (responseBaseVo != null) {
                return responseBaseVo.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("[券平台查券] 查券异常，ticketId:{}", ticketId, e);
            return null;
        }
    }

    /**
     * 根据券包id查询券包信息
     *
     * @param ticketPackageId
     * @return
     */
    public TicketPackageDTO getTicketPackage(String ticketPackageId) {
        if (ticketPackageId == null) {
            return null;
        }
        try {
            GetTicketPackageRequest ticketPackageRequest = new GetTicketPackageRequest();
            ticketPackageRequest.setTicket_package_id(ticketPackageId);
            ResponseBaseVo<TicketPackageDTO> responseBaseVo =
                    ticketPackageClient.getTicketPackage(ticketPackageRequest);
            if (responseBaseVo != null) {
                return responseBaseVo.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("[券平台查券包] 查券异常，ticketId:{}", ticketPackageId, e);
            return null;
        }
    }

    /**
     * 查询用户优惠券信息
     *
     * @param userTicketId
     * @param uid
     * @return
     */
    public UserTicketDTO getUserTicketInfo(String userTicketId, String uid) {
        if (StringUtils.isEmpty(userTicketId)) {
            return null;
        }
        UserTicketsRequest request = new UserTicketsRequest();
        request.setUid(uid);
        request.setUser_ticket_id(userTicketId);
        ResponseBaseVo<UserTicketDTO> responseBaseVo = userTicketClient.getUserTicketById(request);
        return responseBaseVo.getData();
    }

    /**
     * 批量查询用户优惠券
     *
     * @param userTicketIds
     * @param uid
     * @return
     */
    public List<UserTicketDTO> getUserTicketInfoList(List<String> userTicketIds, String uid) {
        if (CollectionUtils.isEmpty(userTicketIds)) {
            return null;
        }
        UserTicketsRequest request = new UserTicketsRequest();
        request.setUid(uid);
        request.setUser_ticket_ids(userTicketIds);
        ResponseBaseVo<List<UserTicketDTO>> responseBaseVo = userTicketClient.listUserTicketByIds(request);
        return responseBaseVo.getData();
    }

    /**
     * 根据券包id查询券id列表
     *
     * @param ticketPackageId
     * @return
     */
    public List<String> getTicketInfoList(String ticketPackageId) {
        if (StringUtils.isEmpty(ticketPackageId)) {
            return null;
        }
        GetTicketPackageRequest request = new GetTicketPackageRequest();
        request.setTicket_package_id(ticketPackageId);
        log.info("根据券包id查券id列表结束，ticketPackageId：{}", ticketPackageId);
        ResponseBaseVo<TicketPackageDTO> responseBaseVo = ticketPackageClient.getTicketPackage(request);
        if (responseBaseVo == null || responseBaseVo.getData() == null) {
            log.info("根据券包id查券id列表结束，res：null");
            return null;
        }
        return responseBaseVo.getData().getCoupon_ids();
    }

    public List<TicketDTO> selectUsefullTicket(QueryTicketListParam queryTicketListParam) {
        ListSimpleTicketVo.Req request = new ListSimpleTicketVo.Req();
        request.setStatusList(Arrays.asList(TICKET_STATUS_PUBLISH, TICKET_STATUS_ACTIVE));
        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        pageInfoDTO.setPageIndex(1);
        pageInfoDTO.setPageSize(QUERY_MAX_SIZE);
        request.setPageInfo(pageInfoDTO);
        String appKey = "promoequity-service";
        request.setAppName(appKey);
        request.setIdNumberOrName(queryTicketListParam.getKeywords());
        VoucherCoreSignUtils.generateRequestParam(request, "iweui28yu8219ui9i9i");
        AuthInfo authInfo = AuthUtil.getAuthInfo();
        //TODO
        if (authInfo == null) {//测试阶段。默认下
            authInfo = new AuthInfo();
            authInfo.setToken("60a387b6fe854aabfe7d85b7");
            authInfo.setUsername("qzm");
            authInfo.setEmail("xxx");
        }
        try {
            ResponseBaseVo<PageQueryResult<ListSimpleTicketVo.Resp>> res =
                    ticketAdminApi.pageQueryBySimple(JSONUtil.toJsonStr(authInfo), request);
            if (res.isSuccess() && !CollectionUtils.isEmpty(res.getData().getRows())) {
                return res.getData().getRows().stream().map(item -> {
                    TicketDTO ticketDTO = new TicketDTO();
                    ticketDTO.setId(item.getId());
                    ticketDTO.setId_number(item.getIdNumber());
                    ticketDTO.setType(item.getType());
                    ticketDTO.setName(item.getName());
                    ticketDTO.setMoney(item.getMoney());
                    ticketDTO.setDiscount(item.getDiscount());
                    ticketDTO.setPay_min(item.getPayMin());
                    ticketDTO.setValid_type(item.getValidType());
                    ticketDTO.setValid_days(item.getValidDays());
                    ticketDTO.setBegin_time(item.getBeginTime());
                    ticketDTO.setEnd_time(item.getEndTime());
                    ticketDTO.setStart_days(item.getStartDays());
                    ticketDTO.setImage(item.getImage());
                    ticketDTO.setDescription(item.getDescription());
                    ticketDTO.setDescription_new(item.getDescriptionNew());
                    ticketDTO.setOperator(item.getOperator());
                    ticketDTO.setMark(item.getMark());
                    ticketDTO.setStatus(item.getStatus());
                    ticketDTO.setCreate_time(item.getCreateTime());
                    ticketDTO.setUpdate_time(item.getUpdateTime());
                    ticketDTO.setCancel_time(item.getCancelTime());
                    ticketDTO.setH5_url(item.getH5Url());
                    ticketDTO.setWxapp_url(item.getWxappUrl());
                    ticketDTO.setNative_url(item.getNativeUrl());
                    ticketDTO.setAliapp_url(item.getAliappUrl());
                    ticketDTO.setIs_new_user_ticket(item.getIsNewUserTicket());
                    ticketDTO.setIs_draw_user_ticket(item.getIsDrawUserTicket());
                    ticketDTO.setIs_ban_balance_ticket(item.getIsBanBalanceTicket());
                    ticketDTO.setNeed_monitor(item.getNeedMonitor());
                    ticketDTO.setLimit_platform(item.getLimitPlatform());
                    ticketDTO.setProduct_map_id(item.getProductMapId());
                    ticketDTO.setRule_type(item.getRuleType());
                    ticketDTO.setNew_product_map_ids(item.getNewProductMapIds());
                    ticketDTO.setTags(item.getTagIds());
                    ticketDTO.setIs_hide_money(item.getIsHideMoney());
                    ticketDTO.setJump_product_package(item.getJumpProductPackage());
                    ticketDTO.setPush_title(item.getPushTitle());
                    ticketDTO.setPush_content(item.getPushContent());
                    ticketDTO.setAllow_vip_card_id(item.getAllowVipCardId());
                    ticketDTO.setAllow_vip_days(item.getAllowVipDays());
                    ticketDTO.setLimit_address(item.getLimitAddressMultiWithName());
                    ticketDTO.setLimit_address_multi(item.getLimitAddressMultiWithName());
                    ticketDTO.setGift_product_ids(item.getGiftProductIds());
                    ticketDTO.setBusiness_scene(item.getBusinessScene());
                    ticketDTO.setNew_rules_select_type(item.getNewRulesSelectType());
                    ticketDTO.setBusiness_scene(item.getBusinessScene());
                    return ticketDTO;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("[券平台查券] 查券列表异常，queryTicketListParam:{}", queryTicketListParam, e);
            return null;
        }
        return Lists.newArrayList();
    }


    public TicketInfoStatistics queryTicketUsed(ListTicketBillParam param) {
        if (StringUtils.isEmpty(param.getTicketId()) || StringUtils.isEmpty(param.getPrizeId())
                || StringUtils.isEmpty(param.getActivityId())) {
            return null;
        }
        ListTicketBillRequest request = new ListTicketBillRequest();
        request.setTicketId(param.getTicketId());
        request.setActivityId(param.getActivityId());
        request.setPrizeId(param.getPrizeId());
        try {
            ResponseBaseVo<List<TicketBillVO>> res = ticketBillAdminApi.listTicketBill(request);
            if (!res.isSuccess()) {
                log.error("[券平台查询券统计] 失败,request:{},response:{}", param, res);
                return null;
            }
            if (CollectionUtils.isEmpty(res.getData())) {
                return null;
            }
            Long sendCount = 0L;
            Long usedCount = 0L;
            for (TicketBillVO ticketBillModel : res.getData()) {
                if (TicketBillTypeEnum.TICKET_BILL_RECEIVE.getCode().equals(ticketBillModel.getType())) {
                    sendCount = ticketBillModel.getCount();

                } else if (TicketBillTypeEnum.MONGO_BILL_WRITE_OFF.getCode().equals(ticketBillModel.getType())) {
                    usedCount = ticketBillModel.getCount();
                }
            }
            return TicketInfoStatistics.builder().usedCount(usedCount).sendCount(sendCount).build();
        } catch (Exception e) {
            log.error("[券平台查询券统计] 异常,request:{}", param, e);
        }
        return null;
    }
}
