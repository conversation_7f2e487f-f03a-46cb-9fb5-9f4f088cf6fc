package com.ddmc.promoequity.infra.convert;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class EquityGroupConvert {
    private TbPromotionEquityGroup baseConvert(PromotionEquityGroupDO promotionEquityGroupDO) {
        if (promotionEquityGroupDO == null) {
            return null;
        }
        TbPromotionEquityGroup result = new TbPromotionEquityGroup();
        result.setGroupId(promotionEquityGroupDO.getGroupId());
        result.setGroupName(promotionEquityGroupDO.getGroupName());
        result.setDescription(promotionEquityGroupDO.getDescription());
        if (promotionEquityGroupDO.getBizLine() != null) {
            result.setBizLine(promotionEquityGroupDO.getBizLine().name());
        }
        if (promotionEquityGroupDO.getUseScene() != null) {
            result.setUseScene(promotionEquityGroupDO.getUseScene().name());
        }
        result.setCreator(promotionEquityGroupDO.getCreator());
        result.setCreateTime(promotionEquityGroupDO.getCreateTime());
        result.setUpdateTime(promotionEquityGroupDO.getUpdateTime());
        result.setPublishTime(promotionEquityGroupDO.getPublishTime());
        if (promotionEquityGroupDO.getStatus() != null) {
            result.setStatus(promotionEquityGroupDO.getStatus().name());
        }
        return result;
    }

    public TbPromotionEquityGroup convertCreate(PromotionEquityGroupDO promotionEquityGroupDO) {
        TbPromotionEquityGroup baseConvert = baseConvert(promotionEquityGroupDO);
        if (baseConvert != null) {
            baseConvert.setGroupId(null);
            baseConvert.setStatus(BizStatus.NEW.name());
            baseConvert.setCreateTime(null);
            baseConvert.setUpdateTime(null);
            baseConvert.setPublishTime(null);
            baseConvert.setValid(null);
        }
        return baseConvert;
    }

    public List<TbPromotionEquity> convertItemCreate(List<PromotionEquityDO> equityList) {
        if (CollectionUtils.isEmpty(equityList)) {
            return Lists.newArrayList();
        }

        List<TbPromotionEquity> toBatchCreate = equityList.stream().map(item -> {
            TbPromotionEquity equity = new TbPromotionEquity();
            equity.setEquityId(item.getEquityId());
            return equity;
        }).collect(Collectors.toList());

        return toBatchCreate;
    }

    public TbPromotionEquityGroup convertUpdate(PromotionEquityGroupDO groupDO) {
        TbPromotionEquityGroup baseConvert = baseConvert(groupDO);
        if (baseConvert != null) {
            baseConvert.setStatus(null);
            baseConvert.setUpdateTime(null);
            baseConvert.setValid(null);
            baseConvert.setCreateTime(null);
            baseConvert.setPublishTime(null);
        }
        return baseConvert;
    }
}
