package com.ddmc.promoequity.infra;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.metrics.Timer;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.StatusCode;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.AttributeKeys;
import com.ddmc.promoequity.config.ApolloConfig;
import com.ddmc.promoequity.enums.EquityTypeEnums;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class EquityMonitor {
    public static final String SUCCESS = "0";
    public static final String FAIL = "-1";
    public static final String EQUITY_SEND = "EQUITY_SEND";

    @Autowired
    private ApolloConfig apolloConfig;

    public void equitySend(EquityTypeEnums equityTypeEnums, boolean success, Object data) {
        if (apolloConfig.monitorTransferSwitch) {
            // 新监控埋点Metrics.Counter
            try {
                Span span = Traces.spanBuilder(EQUITY_SEND).setAttribute("name", equityTypeEnums.name()).startSpan();
                Metrics.newCounter(EQUITY_SEND)
                        .build()
                        .once(Attributes.of(AttributeKey.stringKey("name"), equityTypeEnums.name()));
                span.setStatus(success ? StatusCode.OK : StatusCode.ERROR);
                if (Objects.nonNull(data)) {
                    String eventData = JSON.toJSONString(data);
                    span.addEvent(eventData.length() > 1024 ? eventData.substring(0, 1024) : eventData);                }
                span.end();
            } catch (Exception ex) {
                log.error("[新监控埋点异常] type:{}, name:{}, status:{}, data:{}", EQUITY_SEND, equityTypeEnums.name(), success, JSON.toJSONString(data), ex);
            }
        }
    }


    /**
     * 新监控打点 Metrics.counter + Trace
     * 对应cat Event
     * @param type
     * @param name
     * @param status
     * @param data
     */
    public static void counter(String type, String name, String status, Object data) {
        try {
            Span span = Traces.spanBuilder(type).setAttribute("name", name).startSpan();
            Metrics.newCounter(type)
                    .build()
                    .once(Attributes.of(AttributeKey.stringKey("name"), name));
            span.setStatus(StringUtils.equals(status, SUCCESS) ? StatusCode.OK : StatusCode.ERROR);
            if (Objects.nonNull(data)) {
                String eventData = JSON.toJSONString(data);
                span.addEvent(eventData.length() > 1024 ? eventData.substring(0, 1024) : eventData);
            }
            span.end();
        } catch (Exception ex) {
            log.error("[新监控埋点异常] type:{}, name:{}, status:{}, data:{}", type, name, status, JSON.toJSONString(data), ex);
        }
    }


    /**
     * 新监控打点 Metrics.timer + Trace
     * 对应cat.transaction
     * @param type
     * @param name
     * @param status
     * @param duration
     * @param dataList
     */
    public static void timer(String type, String name, String status, Long duration, List<String> dataList) {
        try {
            Timer timer = Metrics.newTimer(type).build();
            Span span = Traces.spanBuilder(type).setAttribute("name", name).startSpan();
            span.setStatus(StringUtils.equals(status, SUCCESS) ? StatusCode.OK : StatusCode.ERROR);
            if (CollUtil.isNotEmpty(dataList)) {
                String eventData = JSON.toJSONString(dataList);
                span.addEvent(eventData.length() > 1024 ? eventData.substring(0, 1024) : eventData);
            }
            span.end();
            timer.value(Objects.nonNull(duration) ? duration : 0L, Attributes.of(AttributeKey.stringKey("name"), name));
        } catch (Exception ex) {
            log.error("[新监控埋点异常] type:{}, name:{}, status:{}, data:{}", type, name, status, JSON.toJSONString(dataList), ex);
        }
    }

    /**
     * 计数指标 - trace记录携带数据
     */
    public static void logEvent(String type, String name, String status, Object data) {
        Span span = Traces.spanBuilder(type, Instrumentation.EVENT_NEW).startSpan();
        Metrics.newCounter(type, Instrumentation.EVENT_NEW).build().once(Attributes.of(AttributeKey.stringKey("name"), name));
        span.metricsMapping(type, Attributes.of(AttributeKey.stringKey("name"), name,
                AttributeKey.stringKey(AttributeKeys.METRIC_STATUS), status));
        if (data != null) {
            span.addEvent("event_data", Attributes.of(AttributeKey.stringKey("data"), JSON.toJSONString(data)));
        }
        span.end();
    }
}
