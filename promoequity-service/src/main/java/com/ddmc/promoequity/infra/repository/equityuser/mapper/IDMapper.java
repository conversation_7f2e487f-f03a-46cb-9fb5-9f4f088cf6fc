package com.ddmc.promoequity.infra.repository.equityuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IDMapper extends BaseMapper {
    @Select("SELECT next_value FROM dal_dual WHERE seq_name = 'composed_seq' AND biz = 'command_id' AND user_id = " +
            "#{userId}")
    String getNextCommandId(String userId);

    @Select("SELECT next_value FROM dal_dual WHERE seq_name = 'composed_seq' AND biz = 'record_id' AND user_id = " +
            "#{userId}")
    String getNextRecordId(String userId);
}
