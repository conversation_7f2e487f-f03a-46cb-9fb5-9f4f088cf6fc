package com.ddmc.promoequity.infra.repository.equityuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface PromotionEquitySendCommandMapper extends BaseMapper<TbPromotionEquitySendCommand> {
    @Update("update tb_promotion_equity_send_command set status=#{status} where command_id =#{commandId}")
    long updateByCommandId(String commandId, String status);

    @Update("update tb_promotion_equity_send_command set status=#{status} ,extends_info =#{extendsInfo} where " +
            "command_id =#{commandId}")
    long updateExtendsByCommandId(String commandId, String status, String extendsInfo);

    @Select("select * from tb_promotion_equity_send_command where command_id =#{commandId}")
    TbPromotionEquitySendCommand selectByCommandId(String commandId);

    @Select("select * from tb_promotion_equity_send_command where biz_from = #{bizFrom} and biz_no = #{bizNo}" +
            " and user_id =#{uid}")
    TbPromotionEquitySendCommand queryByUnique(String bizFrom, String bizNo, String uid);

    @Select("select * from tb_promotion_equity_send_command where user_id = #{userId} order by id desc")
    List<TbPromotionEquitySendCommand> queryUserCommand(String uid);

    @Select("select * from tb_promotion_equity_send_command where user_id = #{uid} and biz_from = #{bizFrom} " +
            "and id > #{preCommandId} and valid = 1 limit ${size} ")
    List<TbPromotionEquitySendCommand> selectListByPreKey(String bizFrom, String uid, Long size, Long preCommandId);

    @Select("select count(*) from tb_promotion_equity_send_command where user_id = #{uid} and biz_from = #{bizFrom} " +
            "and id > #{preCommandId} and valid = 1")
    long countByPreKey(String uid, String bizFrom, Long preCommandId);
}
