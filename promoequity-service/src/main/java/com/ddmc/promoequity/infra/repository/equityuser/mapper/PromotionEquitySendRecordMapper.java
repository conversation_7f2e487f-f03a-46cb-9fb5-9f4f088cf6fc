package com.ddmc.promoequity.infra.repository.equityuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.dto.EquityRecordBizCountStatisticDTO;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PromotionEquitySendRecordMapper extends BaseMapper<TbPromotionEquitySendRecord> {

    @Select("select * from tb_promotion_equity_send_record where command_id = #{commandId}")
    List<TbPromotionEquitySendRecord> findByCommandId(String commandId);

    @Select("select user_id ,sum(biz_count) as biz_count from tb_promotion_equity_send_record where user_id = #{uid} " +
            "and " +
            "equity_type=#{equityType} " +
            "and biz_key = #{bizKey} and status='SUCCESS'")
    TbPromotionEquitySendRecord summaryBizCount(EquityRecordBizCountStatisticDTO request);
}
