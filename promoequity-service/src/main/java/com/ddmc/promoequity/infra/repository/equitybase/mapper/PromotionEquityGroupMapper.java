package com.ddmc.promoequity.infra.repository.equitybase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;

@Mapper
public interface PromotionEquityGroupMapper extends BaseMapper<TbPromotionEquityGroup> {

    @Update("update tb_promotion_equity_group set status = #{toStatus},publish_time = now() where  group_id = " +
            "#{groupId} and status = #{fromStatus}")
    long updateState(Serializable groupId, String fromStatus, String toStatus);
}
