package com.ddmc.promoequity.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.promoequity.common.constant.MonitorConstants;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.dto.EquityRecordBizCountStatisticDTO;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityMapper;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquitySendStatisticsMapper;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendRecord;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendRecordMapper;
import com.ddmc.promoequity.utils.CatUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Repository
public class PromotionEquityRepositoryImpl implements PromotionEquityRepository {

    /**
     * equitySendStatistics query 开关
     */
    @Value("${promoequity.equitySendStatisticsQuery.switch:false}")
    private boolean equitySendStatisticsQuerySwitch;

    @Resource
    private PromotionEquityMapper promotionEquityMapper;

    @Resource
    private PromotionEquitySendStatisticsMapper promotionEquitySendStatisticsMapper;
    @Resource
    private PromotionEquitySendRecordMapper promotionEquitySendRecordMapper;

    @Override
    public TbPromotionEquity findById(Serializable id) {
        TbPromotionEquity tbPromotionEquity = promotionEquityMapper.selectById(id);
        return tbPromotionEquity;
    }


    @Override
    public void insertOrUpdate(TbPromotionEquity tbPromotionEquity) {
        if (tbPromotionEquity == null) {
            return;
        }
        if (tbPromotionEquity.getEquityId() != null) {
            promotionEquityMapper.updateById(tbPromotionEquity);
        } else {
            promotionEquityMapper.insert(tbPromotionEquity);
        }
    }

    @Override
    public long updateStatus(Serializable id, BizStatus from, BizStatus to) {
        return promotionEquityMapper.updateState(id, from.name(), to.name());
    }

    @Override
    public PageResult<TbPromotionEquity> queryList(PromotionEquityQuery query, PageInfo pageInfo) {
        Page page = new Page(pageInfo.getIndex(), pageInfo.getSize());

        QueryWrapper<TbPromotionEquity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("valid", 1);
        if (!StringUtils.isEmpty(query.getKeywords())) {
            queryWrapper.eq("equity_name", query.getKeywords());
        }
        if (!StringUtils.isEmpty(query.getBizKey())) {
            queryWrapper.eq("biz_key", query.getBizKey());
        }
        if (query.getBizStatus() != null) {
            queryWrapper.eq("status", query.getBizStatus().name());
        }
        if (query.getEquityTypeEnums() != null) {
            queryWrapper.eq("equity_type", query.getEquityTypeEnums().name());
        }
        queryWrapper.orderByDesc("equity_id");
        Page<TbPromotionEquity> pageList = promotionEquityMapper.selectPage(page, queryWrapper);
        PageResult<TbPromotionEquity> result = new PageResult<>();
        result.setIndex(page.getCurrent());
        result.setSize(page.getSize());
        result.setCount(page.getTotal());
        result.setTotal(page.getPages());
        if (pageList.getRecords() != null) {
            result.setResult(pageList.getRecords());
        }
        return result;
    }

    @Override
    public List<TbPromotionEquity> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return promotionEquityMapper.selectBatchIds(ids);
    }

    @Override
    public List<TbPromotionEquitySendStatistics> sendStatistics(String sendBizFrom, String sendBizKey) {
        // Apollo 开关控制 - 放在最开始
        if (equitySendStatisticsQuerySwitch) {
            CatUtils.logEvent(MonitorConstants.EquitySendStatistics.EQUITY_SEND_STATISTICS_QUERY,
                    "switch_on", sendBizKey);
            log.info("equitySendStatistics query switch is on, sendBizFrom: {}, sendBizKey: {}",
                    sendBizFrom, sendBizKey);
            return Lists.newArrayList();
        }

        // 老逻辑打点
        CatUtils.logEvent(MonitorConstants.EquitySendStatistics.EQUITY_SEND_STATISTICS_QUERY,
                "old_logic", sendBizKey);

        if (StringUtils.isEmpty(sendBizFrom) && StringUtils.isEmpty(sendBizKey)) {
            return Lists.newArrayList();
        }

        if (StringUtils.isEmpty(sendBizKey)) {
            sendBizKey = sendBizFrom;
        }

        TbPromotionEquitySendStatistics tbPromotionEquitySendStatistics = new TbPromotionEquitySendStatistics();
        //因为写入的地方发生了变化，所以需要修改读取逻辑
//        tbPromotionEquitySendStatistics.setSendBizFrom(sendBizFrom);
        tbPromotionEquitySendStatistics.setSendBizKey(sendBizKey);
        List<TbPromotionEquitySendStatistics> datas =
                promotionEquitySendStatisticsMapper.selectList(new QueryWrapper<>(tbPromotionEquitySendStatistics));
        CatUtils.logEvent("EquitySendStatistic", "selectList");
        return datas;
    }

    @Override
    public EquityRecordBizCountStatisticDTO bizCountRecordStatistics(EquityRecordBizCountStatisticDTO request) {
        TbPromotionEquitySendRecord data =
                promotionEquitySendRecordMapper.summaryBizCount(request);

        return EquityRecordBizCountStatisticDTO.builder()
                .uid(request.getUid())
                .equityType(request.getEquityType())
                .totalCnt(data == null ? new BigDecimal(0) : data.getBizCount())
                .bizKey(request.getBizKey())
                .build();
    }

}
