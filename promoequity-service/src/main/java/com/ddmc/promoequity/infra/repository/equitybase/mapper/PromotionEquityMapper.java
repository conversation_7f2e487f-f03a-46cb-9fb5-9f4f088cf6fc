package com.ddmc.promoequity.infra.repository.equitybase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;

@Mapper
public interface PromotionEquityMapper extends BaseMapper<TbPromotionEquity> {

    @Update("update tb_promotion_equity set status = #{toStatus},publish_time = now() where  equity_id = #{equityId} " +
            "and status = #{fromStatus}")
    long updateState(Serializable equityId, String fromStatus, String toStatus);

    @Update("select equity_id from tb_promotion_equity where equity_id = #{equityId} for update ")
    Long lock(Long equityId);

}
