package com.ddmc.promoequity.infra.factory;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.enums.BizLineEnums;
import com.ddmc.promoequity.enums.UseSceneEnums;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class EquityGroupFactory {
    @Resource
    private PromotionEquityFactory promotionEquityFactory;

    public PromotionEquityGroupDO createGroupDO(TbPromotionEquityGroup group) {
        return createGroupDO(group, null);
    }

    public PromotionEquityGroupDO createGroupDO(TbPromotionEquityGroup group, List<TbPromotionEquity> equityList) {
        if (group == null) {
            return null;
        }
        PromotionEquityGroupDO result = new PromotionEquityGroupDO();
        result.setGroupId(group.getGroupId());
        result.setGroupName(group.getGroupName());
        result.setDescription(group.getDescription());
        result.setBizLine(BizLineEnums.get(group.getBizLine()));
        result.setUseScene(UseSceneEnums.get(group.getUseScene()));
        result.setCreator(group.getCreator());
        result.setCreateTime(group.getCreateTime());
        result.setUpdateTime(group.getUpdateTime());
        result.setPublishTime(group.getPublishTime());
        result.setStatus(BizStatus.get(group.getStatus()));

        if (!CollectionUtils.isEmpty(equityList)) {
            result.setEquityList(
                    equityList.stream().map(promotionEquityFactory::createEquity).collect(Collectors.toList())
            );
        } else {
            result.setEquityList(Lists.newArrayList());
        }

        return result;
    }
}
