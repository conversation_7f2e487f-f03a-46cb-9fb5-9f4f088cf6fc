package com.ddmc.promoequity.infra.facade;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.vo.PointExchangeVipInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.vip.app.client.internal_api.VipInternalRechargeApiClient;
import com.ddmc.vip.app.request.internal_api.ExchangeVipReqDTO;
import com.ddmc.vip.app.response.internal_api.ExchangeVipDaysResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * vip客户端外观
 *
 * <AUTHOR>
 * @date 2023/10/31
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VipClientFacade {

    private final VipInternalRechargeApiClient vipInternalRechargeApiClient;

    /**
     * 兑换会员
     */
    public ExchangeVipDaysResDTO exchangeVip(EquitySendCommandDO command) {
        ExchangeVipReqDTO vipRequest = new ExchangeVipReqDTO();
        vipRequest.setExchangeReqNo(command.getBizNo());
        vipRequest.setUid(command.getUid());
        //
        PointExchangeVipInfo exchangeVipInfo = Optional.of(command)
                .map(EquitySendCommandDO::getPropsInfo)
                .map(PropsInfo::getExchangeVipInfo)
                .orElseThrow(() -> new RuntimeException("积分兑换信息不存在,请确认权益配置"));
        //纯代码,理论2行可能出现NPE,理论不会
        vipRequest.setExchangeVipDays(exchangeVipInfo.getExchangeVipDays().longValue());
        vipRequest.setSourceExchangeExpandValue(exchangeVipInfo.getNeedPointNum().toString());
        vipRequest.setSource(exchangeVipInfo.getSource());
        //rpc
        ResponseBaseVo<ExchangeVipDaysResDTO> response = this.vipInternalRechargeApiClient.exchangeVipDays(vipRequest);
        if (null != response && response.isSuccess() && null != response.getData()) {
            return response.getData();
        } else {
            return null;
        }
    }

}
