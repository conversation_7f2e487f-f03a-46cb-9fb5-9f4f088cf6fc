package com.ddmc.promoequity.infra.repository.equityuser.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class TbPromotionEquitySendCommand {

    @TableId(type= IdType.AUTO)
    private Long id;
    private String commandId;
    private Long equityId;
    private String equityType;
    private String userId;
    private String bizFrom;
    private String bizNo;
    private Date createTime;
    private Date updateTime;
    private String props;
    private String extendsInfo;
    private String status;
    private Integer valid;
}
