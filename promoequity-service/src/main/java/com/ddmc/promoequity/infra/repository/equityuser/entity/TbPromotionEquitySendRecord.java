package com.ddmc.promoequity.infra.repository.equityuser.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TbPromotionEquitySendRecord {

    @TableId(type= IdType.AUTO)
    private Long id;
    private String recordId;
    private String commandId;
    private String userId;
    private String status;
    private String comment;
    private Date createTime;
    private Integer valid;

    private String bizFrom;
    private String bizKey;
    private String equityType;
    private Long equityId;
    private BigDecimal bizCount;
}
