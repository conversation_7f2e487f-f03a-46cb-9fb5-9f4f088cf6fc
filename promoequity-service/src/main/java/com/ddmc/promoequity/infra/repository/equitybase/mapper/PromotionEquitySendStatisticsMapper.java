package com.ddmc.promoequity.infra.repository.equitybase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface PromotionEquitySendStatisticsMapper extends BaseMapper<TbPromotionEquitySendStatistics> {

//    @Select("select * from tb_promotion_equity_send_statistics where send_biz_from =#{sendBizFrom} and equity_id = " +
//            "#{equityId} limit 1")
//    TbPromotionEquitySendStatistics queryByBizFrom(String sendBizFrom, Long equityId);
    @Select("select * from tb_promotion_equity_send_statistics where send_biz_key =#{sendBizKey} and equity_id = " +
            "#{equityId} limit 1")
    TbPromotionEquitySendStatistics queryByBizKey(String sendBizKey, Long equityId);

    @Select("update tb_promotion_equity_send_statistics set send_count = send_count + #{num},biz_count = #{bizCount} where" +
            " id = #{id}")
    Long increaseSendCount(Long id, Long num, BigDecimal bizCount);
}
