package com.ddmc.promoequity.infra.convert;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.utils.json.JsonUtil;
import org.springframework.stereotype.Component;

@Component
public class PromotionEquityConvert {

    private TbPromotionEquity baseConvert(PromotionEquityDO promotionEquityDO) {
        if (promotionEquityDO == null) {
            return null;
        }
        TbPromotionEquity result = new TbPromotionEquity();
        result.setEquityId(promotionEquityDO.getEquityId());
        if (promotionEquityDO.getEquityType() != null) {
            result.setEquityType(promotionEquityDO.getEquityType().name());
        }
        result.setEquityName(promotionEquityDO.getEquityName());
        if (promotionEquityDO.getBizFrom() != null) {
            result.setBizFrom(promotionEquityDO.getBizFrom().name());
        }
        result.setBizKey(promotionEquityDO.getBizKey());
        if (promotionEquityDO.getExpireType() != null) {
            result.setExpireType(promotionEquityDO.getExpireType().name());
        }
        result.setExpireTime(promotionEquityDO.getExpireTime());
        result.setDescription(promotionEquityDO.getDescription());
        result.setCreator(promotionEquityDO.getCreator());
        result.setCreateTime(promotionEquityDO.getCreateTime());
        result.setUpdateTime(promotionEquityDO.getUpdateTime());
        result.setPublishTime(promotionEquityDO.getPublishTime());
        if (promotionEquityDO.getStatus() != null) {
            result.setStatus(promotionEquityDO.getStatus().name());
        }
        if (promotionEquityDO.getPropsInfo() != null) {
            result.setProps(JsonUtil.toJSON(promotionEquityDO.getPropsInfo()));
        }

        return result;
    }

    public TbPromotionEquity convertCreatePromotionEquity(PromotionEquityDO promotionEquityDO) {
        TbPromotionEquity result = baseConvert(promotionEquityDO);
        if (result != null) {
            result.setStatus(BizStatus.NEW.name());
            result.setCreateTime(null);
            result.setUpdateTime(null);
            result.setPublishTime(null);
            result.setValid(null);
        }
        return result;
    }

    public TbPromotionEquity convertUpdatePromotionEquity(PromotionEquityDO source, PromotionEquityDO origin) {
        if (source == null || origin == null) {
            return null;
        }
        TbPromotionEquity result = baseConvert(source);
        //补全其他信息
        if (result.getEquityType() == null) {
            result.setEquityType(origin.getEquityType().name());
        }
        if (result.getEquityName() == null) {
            result.setEquityName(origin.getEquityName());
        }
        if (result.getBizFrom() == null) {
            result.setBizFrom(origin.getBizFrom().name());
        }
        if (result.getBizKey() == null) {
            result.setBizKey(origin.getBizKey());
        }
        if (result.getExpireType() == null) {
            result.setExpireType(origin.getExpireType().name());
        }
        if (result.getExpireTime() == null) {
            result.setExpireTime(origin.getExpireTime());
        }
        if (result.getDescription() == null) {
            result.setDescription(origin.getDescription());
        }
//        if (result.getProps() == null) {
//            if (origin.getProps() != null) {
//                result.setProps(JsonUtil.toJSON(origin.getProps()));
//            }
//        }
        if (result.getProps() == null) {
            if (origin.getPropsInfo() != null) {
                result.setProps(JsonUtil.toJSON(origin.getPropsInfo()));
            }
        }

        if (result != null) {
            result.setStatus(null);
            result.setValid(null);
        }
        return result;
    }
}
