package com.ddmc.promoequity.controller;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.gateway.bg.client.annotation.HttpApiGroup;
import com.ddmc.gateway.bg.client.annotation.HttpApiPostMapping;
import com.ddmc.promoequity.controller.convert.EquityGroupControllerConvert;
import com.ddmc.promoequity.controller.validator.EquityGroupControllerValidator;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityGroupQuery;
import com.ddmc.promoequity.domain.service.EquityGroupDomainService;
import com.ddmc.promoequity.dto.EquityGroupDTO;
import com.ddmc.promoequity.dto.PageDTO;
import com.ddmc.promoequity.dto.request.QueryEquityGroupListRequest;
import com.ddmc.promoequity.dto.request.UpdateEquityGroupStateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@HttpApiGroup("权益分组管理")
@RestController
@RequestMapping(value = "/admin/equity_group")
public class EquityGroupAdminController {

    @Resource
    private EquityGroupDomainService equityGroupDomainService;
    @Resource
    private EquityGroupControllerValidator equityGroupControllerValidator;
    @Resource
    private EquityGroupControllerConvert equityGroupControllerConvert;

    @HttpApiPostMapping(value = "/queryList", name = "查询权益分组列表")
    public ResponseBaseVo<PageDTO<EquityGroupDTO>> queryList(@RequestBody QueryEquityGroupListRequest queryEquityListRequest) {
        try {
            PromotionEquityGroupQuery query = PromotionEquityGroupQuery.builder().build();
            PageResult<PromotionEquityGroupDO> pageResult = equityGroupDomainService.queryList(query,
                    new PageInfo(queryEquityListRequest.getPageIndex(), queryEquityListRequest.getPageSize()));

            PageDTO<EquityGroupDTO> result = equityGroupControllerConvert.convertList(pageResult);
            return ResponseBaseVo.ok(result);
        } catch (Exception e) {
            log.error("[查询权益组列表]-接口出错,request:{}",queryEquityListRequest, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/detail", name = "查询权益分组详情")
    public ResponseBaseVo<EquityGroupDTO> detail(@RequestParam("groupId") Long groupId) {
        try {
            PromotionEquityGroupDO groupDO = equityGroupDomainService.queryEquityGroup(groupId);
            ResponseBaseVo<EquityGroupDTO> result = ResponseBaseVo.ok(equityGroupControllerConvert.convertDO(groupDO));
            return result;
        } catch (Exception e) {
            log.error("[查询权益组详细]-接口出错，groupId:{}", groupId, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/saveOrUpdate", name = "更新或保存权益分组")
    public ResponseBaseVo<EquityGroupDTO> saveOrUpdate(@RequestBody EquityGroupDTO equityDTO) {
        try {
            equityGroupControllerValidator.validatorSaveOrUpdate(equityDTO);
            PromotionEquityGroupDO promotionEquityGroupDO = equityGroupControllerConvert.convertDTO(equityDTO);
            if (promotionEquityGroupDO.getGroupId() == null) {
                equityGroupDomainService.createEquityGroup(promotionEquityGroupDO);
            } else {
                equityGroupDomainService.updateEquityGroup(promotionEquityGroupDO);
            }
            PromotionEquityGroupDO result =
                    equityGroupDomainService.queryEquityGroup(promotionEquityGroupDO.getGroupId());
            return ResponseBaseVo.ok(equityGroupControllerConvert.convertDO(result));
        } catch (Exception e) {
            log.error("[更新修改权益]-接口出错,equityDTO:{}", equityDTO, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/publish", name = "发布权益分组")
    public ResponseBaseVo publish(@RequestBody UpdateEquityGroupStateRequest request) {
        try {
            Assert.notNull(request.getGroupId(), "权益组ID不能为空");
            equityGroupDomainService.publish(request.getGroupId());
            return ResponseBaseVo.ok();
        } catch (Exception e) {
            log.error("[发布权益组]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/abandon", name = "废弃权益分组")
    public ResponseBaseVo abandon(@RequestBody UpdateEquityGroupStateRequest request) {
        try {
            Assert.notNull(request.getGroupId(), "权益组ID不能为空");
            equityGroupDomainService.abandon(request.getGroupId());
            return ResponseBaseVo.ok();
        } catch (Exception e) {
            log.error("[废弃权益组]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

}
