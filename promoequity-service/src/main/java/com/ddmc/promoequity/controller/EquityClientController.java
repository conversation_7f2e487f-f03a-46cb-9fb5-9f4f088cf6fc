package com.ddmc.promoequity.controller;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.controller.convert.EquityControllerConvert;
import com.ddmc.promoequity.controller.validator.EquityClientControllerValidator;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.entity.MorePageResult;
import com.ddmc.promoequity.domain.entity.vo.EquityCommandListQuery;
import com.ddmc.promoequity.domain.service.EquityDomainService;
import com.ddmc.promoequity.dto.MorePageResultDTO;
import com.ddmc.promoequity.dto.SendEquityResult;
import com.ddmc.promoequity.dto.request.SendEquityRequest;
import com.ddmc.promoequity.dto.request.SendRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/client/equity")
public class EquityClientController {
    @Resource
    private EquityDomainService equityDomainService;

    @Resource
    private EquityControllerConvert equityControllerConvert;

    @Resource
    private EquityClientControllerValidator equityClientControllerValidator;

    /**
     * 发权益的时候，
     * bizfrom涉及到权益统计，
     *
     * @param request
     * @return
     */
    @PostMapping("/send")
    public ResponseBaseVo<SendEquityResult> sendEquity(@RequestBody SendEquityRequest request) {
        try {
            EquitySendDO equitySendDO = equityControllerConvert.convertSend(request);
            SendEquityResultBO result = equityDomainService.sendEquity(equitySendDO);
            //初始化返回信息
            if (result.isSuccess()) {
                return equityControllerConvert.convertSendResult(result);
            } else {
                return ResponseBaseVo.fail(result.getErrorCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("[发放权益]-出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @PostMapping("/sendRecord")
    public ResponseBaseVo<MorePageResultDTO<SendEquityResult>> sendRecord(@RequestBody SendRecordRequest request) {
        try {
            equityClientControllerValidator.validator(request);
            EquityCommandListQuery query = EquityCommandListQuery.builder().uid(request.getUid())
                    .bizFrom(request.getBizFrom()).size(request.getSize()).preCommandId(request.getPreKey()).build();

            MorePageResult<EquitySendCommandDO> records = equityDomainService.sendEquityRecord(query);

            MorePageResultDTO<SendEquityResult> result = equityControllerConvert.convertMorePageResult(records);
            return ResponseBaseVo.ok(result);
        } catch (Exception e) {
            log.error("[查询权益记录]-出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
}
