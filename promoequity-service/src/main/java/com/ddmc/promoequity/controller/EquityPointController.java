package com.ddmc.promoequity.controller;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.controller.convert.EquityPointControllerConvert;
import com.ddmc.promoequity.dto.request.PointsOptionRequest;
import com.ddmc.promoequity.infra.facade.entity.PointsOptionParam;
import com.ddmc.promoequity.domain.service.EquityPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/client/equity/point/v1")
public class EquityPointController {

    @Resource
    private EquityPointService equityPointService;
    @Resource
    private EquityPointControllerConvert equityPointControllerConvert;

    /**
     * 增加积分
     * @param request
     * @return
     */
    @PostMapping("/increasePoint")
    public ResponseBaseVo<Boolean> increasePoint(@RequestBody PointsOptionRequest request) {
        try {
            PointsOptionParam param = equityPointControllerConvert.convertParam(request);
            return equityPointService.increasePoint(param);
        } catch (Exception e) {
            log.error("[增加积分]-出错,request:{}",request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
    /**
     * 扣减积分
     * @param request
     * @return
     */
    @PostMapping("/decreasePoint")
    public ResponseBaseVo<Boolean> decreasePoint(@RequestBody PointsOptionRequest request) {
        try {
            PointsOptionParam param = equityPointControllerConvert.convertParam(request);
            return equityPointService.decreasePoint(param);
        } catch (Exception e) {
            log.error("[扣减积分]-出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
    /**
     * 查询用户总积分
     * @param userId
     * @return
     */
    @GetMapping("/getUserTotalPoint")
    public ResponseBaseVo<Integer> getUserTotalPoint(@RequestParam("userId") String userId) {
        try {
            return equityPointService.getUserTotalPoint(userId);
        } catch (Exception e) {
            log.error("[增加积分]-出错,request:{}", userId, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
    /**
     * 奖励积分
     * @param request
     * @return
     */
    @PostMapping("/awardPoint")
    public ResponseBaseVo<Boolean> awardPoint(@RequestBody PointsOptionRequest request) {
        try {
            PointsOptionParam param = equityPointControllerConvert.convertParam(request);
            return equityPointService.awardPoint(param);
        } catch (Exception e) {
            log.error("[奖励积分]-出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
    /**
     * 积分兑换
     * @param request
     * @return
     */
    @PostMapping("/pointExchange")
    public ResponseBaseVo<Boolean> pointExchange(@RequestBody PointsOptionRequest request) {
        try {
            PointsOptionParam param = equityPointControllerConvert.convertParam(request);
            return equityPointService.pointExchange(param);
        } catch (Exception e) {
            log.error("[积分兑换]-出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
}
