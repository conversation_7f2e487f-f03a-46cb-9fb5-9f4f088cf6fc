package com.ddmc.promoequity.controller.validator;

import com.ddmc.promoequity.dto.request.PointsOptionRequest;
import com.ddmc.promoequity.dto.request.SendRecordRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


@Component
public class EquityPointControllerValidator {

    public void validator(PointsOptionRequest request) {
        if (StringUtils.isEmpty(request.getBizFrom())) {
            throw new IllegalArgumentException("bizFrom 不能为空");
        }
        if (StringUtils.isEmpty(request.getUserId())) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (request.getPoint() == null || request.getPoint() <= 0) {
            throw new IllegalArgumentException("point必须为大于0的整数");
        }
    }
}
