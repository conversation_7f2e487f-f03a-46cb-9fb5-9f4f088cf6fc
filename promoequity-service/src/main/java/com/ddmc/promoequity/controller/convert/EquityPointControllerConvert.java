package com.ddmc.promoequity.controller.convert;

import com.ddmc.promoequity.controller.validator.EquityPointControllerValidator;
import com.ddmc.promoequity.dto.request.PointsOptionRequest;
import com.ddmc.promoequity.infra.facade.entity.PointsOptionParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/05/12
 */
@Component
public class EquityPointControllerConvert {

    @Autowired
    private EquityPointControllerValidator equityPointControllerValidator;

    public PointsOptionParam convertParam(PointsOptionRequest request) {
        if (request == null) {
            return null;
        }
        //1、 检验参数
        equityPointControllerValidator.validator(request);
        //1、 封装参数
        PointsOptionParam pointsOptionParam = PointsOptionParam.builder()
                .userId(request.getUserId())
                .point(request.getPoint())
                .description(request.getDescription())
                .requestId(request.getRequestId())
                .type(request.getType())
                .orderNumber(request.getOrderNumber())
                .build();
        return pointsOptionParam;
    }
}
