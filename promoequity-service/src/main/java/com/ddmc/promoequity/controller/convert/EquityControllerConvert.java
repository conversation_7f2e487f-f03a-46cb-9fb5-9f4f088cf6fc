package com.ddmc.promoequity.controller.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ddmc.activitycore.model.UserProductBenefitModel;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.ablity.EquityAbility;
import com.ddmc.promoequity.domain.ablity.PromotionEquityStore;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SelectLabel;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.entity.MorePageResult;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquitySendStatisticsDO;
import com.ddmc.promoequity.dto.*;
import com.ddmc.promoequity.dto.request.SaveOrUpdateEquityRequest;
import com.ddmc.promoequity.dto.request.SendEquityRequest;
import com.ddmc.promoequity.enums.BizFromEnums;
import com.ddmc.promoequity.enums.EquityStatus;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.enums.ExpireTypeEnums;
import com.ddmc.promoequity.infra.facade.UserActivityClientFacade;
import com.ddmc.promoequity.infra.facade.UserTicketClientFacade;
import com.ddmc.promoequity.infra.facade.entity.ListTicketBillParam;
import com.ddmc.promoequity.infra.facade.entity.TicketInfoStatistics;
import com.ddmc.promoequity.vo.BalanceInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.vouchercore.client.dto.TicketDTO;
import com.ddmc.vouchercore.client.dto.TicketPackageDTO;
import com.ddmc.vouchercore.client.dto.UserTicketDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EquityControllerConvert {
    @Resource
    private UserTicketClientFacade userTicketClientFacade;

    @Resource
    private PromotionEquityStore promotionEquityStore;
    @Resource
    private UserActivityClientFacade userActivityClientFacade;

    /**
     * PromotionEquityDO转换EquityDTO
     *
     * @param item
     * @param showExtends
     * @return
     */
    public EquityDTO convertDTO(PromotionEquityDO item, boolean showExtends) {
        if (item == null) {
            return null;
        }
        EquityDTO equityDTO = new EquityDTO();
        equityDTO.setEquityId(item.getEquityId());
        if (item.getEquityType() != null) {
            equityDTO.setEquityType(item.getEquityType().name());
        }
        equityDTO.setEquityName(item.getEquityName());
        equityDTO.setBizKey(item.getBizKey());
        equityDTO.setDescription(item.getDescription());
        equityDTO.setCreator(item.getCreator());
        if (item.getPublishTime() != null) {
            equityDTO.setPublishTime(item.getPublishTime().getTime());
        }
        if (item.getCreateTime() != null) {
            equityDTO.setCreateTime(item.getCreateTime().getTime());
        }
        if (item.getUpdateTime() != null) {
            equityDTO.setUpdateTime(item.getUpdateTime().getTime());
        }
        if (item.getStatus() != null) {
            equityDTO.setStatus(item.getStatus().name());
        }
        equityDTO.setPropsInfo(item.getPropsInfo());
        //如果查询额外信息
        if (showExtends) {
            switch (item.getEquityType()) {
                case COUPON:
                    //设置优惠券信息
                    equityDTO.setTicketDTO(getTicketDTO(item));
                    break;
                case COUPONS:
                    //设置优惠券包信息
                    equityDTO.setTicketPackageDTO(getTicketPackageDTO(item));
                    break;
                case BALANCE:
                    queryAndFieldBalance(equityDTO.getPropsInfo().getBalanceInfo());
                    break;
            }
        }
        return equityDTO;
    }

    public void queryAndFieldBalance(BalanceInfo balanceInfo) {
        String cardId = balanceInfo.getCardId();

    }

    /**
     * PromotionEquityDO转换EquityDTO
     *
     * @param items
     * @param showExtends
     * @return
     */
    public List<EquityDTO> convertDTO(List<PromotionEquityDO> items, boolean showExtends) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.EMPTY_LIST;
        }

        return items.stream().map(o -> convertDTO(o, showExtends)).collect(Collectors.toList());
    }

    /**
     * 调用券获取券信息
     *
     * @param item
     * @return
     */
    private TicketDTO getTicketDTO(PromotionEquityDO item) {
        String ticketId = item.getPropsInfo().getCouponInfo().getTicketId();
        if (!StringUtils.isEmpty(ticketId)) {
            return userTicketClientFacade.getTicket(ticketId);
        }
        return null;
    }

    /**
     * 调用券获取券包信息
     *
     * @param item
     * @return
     */
    private TicketPackageDTO getTicketPackageDTO(PromotionEquityDO item) {
        String ticketPackageId = item.getPropsInfo().getCouponPackageInfo().getTicketPackageId();
        if (!StringUtils.isEmpty(ticketPackageId)) {
            return userTicketClientFacade.getTicketPackage(ticketPackageId);
        }
        return null;
    }

    /**
     * PromotionEquityDO转换EquityDTO默认不展示扩展信息
     *
     * @param item
     * @return
     */
    public EquityDTO convertDTO(PromotionEquityDO item) {
        return convertDTO(item, false);
    }

    /**
     * PromotionEquityDO列表转换EquityDTO列表
     *
     * @param pageResult
     * @return
     */
    public PageDTO<EquityDTO> convertList(PageResult<PromotionEquityDO> pageResult) {
        if (pageResult == null) {
            return null;
        }
        PageDTO<EquityDTO> result = new PageDTO<>();
        result.setIndex(pageResult.getIndex());
        result.setCount(pageResult.getCount());
        result.setPageSize(pageResult.getSize());
        result.setTotalPage(pageResult.getTotal());
        if (!CollectionUtils.isEmpty(pageResult.getResult())) {
            result.setRows(
                    pageResult.getResult().stream().map(item -> this.convertDTO(item)).collect(Collectors.toList())
            );
        }
        return result;
    }

    /**
     * SaveOrUpdateEquityRequest转换PromotionEquityDO
     *
     * @param request
     * @return
     */
    public PromotionEquityDO convertDo(SaveOrUpdateEquityRequest request) {
        if (request == null) {
            return null;
        }
        PromotionEquityDO equityDO = new PromotionEquityDO();
        equityDO.setEquityId(request.getEquityId());
        equityDO.setEquityType(EquityTypeEnums.get(request.getEquityType()));
        equityDO.setEquityName(request.getEquityName());
        //TODO 默认为券平台
        equityDO.setBizFrom(BizFromEnums.COUPON);

        equityDO.setBizKey(getBizKey(request));
        equityDO.setExpireType(ExpireTypeEnums.UN_LIMIT);

        equityDO.setDescription(request.getDescription());
        equityDO.setCreator(request.getCreator());
        if (request.getPublishTime() != null) {
            equityDO.setPublishTime(new Date(request.getPublishTime()));
        }
        if (request.getCreateTime() != null) {
            equityDO.setCreateTime(new Date(request.getCreateTime()));
        }
        if (request.getUpdateTime() != null) {
            equityDO.setUpdateTime(new Date(request.getUpdateTime()));
        }
        equityDO.setStatus(BizStatus.get(request.getStatus()));
        equityDO.setPropsInfo(request.getPropsInfo());
        return equityDO;
    }

    private String getBizKey(SaveOrUpdateEquityRequest request) {
        if (EquityTypeEnums.COUPON.name().equals(request.getEquityType())) {
            if (request.getPropsInfo() != null && request.getPropsInfo().getCouponInfo() != null) {
                return request.getPropsInfo().getCouponInfo().getTicketId();
            }
        }
        if (EquityTypeEnums.CASH_COUPON.name().equals(request.getEquityType())) {
            if (request.getPropsInfo() != null && request.getPropsInfo().getCashCouponInfo() != null) {
                return String.valueOf(request.getPropsInfo().getCashCouponInfo().getCashCouponId());
            }
        }
        return "";
    }


    /**
     * SendEquityRequest转换EquitySendDO
     *
     * @param request
     * @return
     */
    public EquitySendDO convertSend(SendEquityRequest request) {
        if (request == null) {
            return null;
        }

        EquitySendDO equitySendDO = EquitySendDO.builder()
                .equityId(request.getEquityId())
                .bizFrom(request.getBizFrom())
                .bizNo(request.getBizNo())
                .bizKey(request.getBizKey())
                .uid(request.getUid())
                .scene(request.getScene())
                .equityCnt(request.getEquityCnt())
                .description(request.getDescription())
                .stationId(request.getStationId())
                .propsInfo(request.getPropsInfo())
                .build();
        return equitySendDO;
    }

    public List<SelectLabelDTO> convertSelectLabel(List<SelectLabel> result) {
        if (CollectionUtils.isEmpty(result)) {
            return Lists.newArrayList();
        }
        return result.stream().map(label -> {
            SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
            selectLabelDTO.setLabel(label.getLabel());
            selectLabelDTO.setValue(label.getValue());
            return selectLabelDTO;
        }).collect(Collectors.toList());
    }

    public EquityStatisticsGroupDTO convertStatistics(List<PromotionEquitySendStatisticsDO> result) {
        EquityStatisticsGroupDTO equityStatisticsGroupDTO = new EquityStatisticsGroupDTO();
        equityStatisticsGroupDTO.setDataTime(System.currentTimeMillis());
        equityStatisticsGroupDTO.setTotalCount(0L);
        List<EquityStatisticsItemDTO> equityStatistics = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(result)) {
            AtomicLong totalCount = new AtomicLong(0);
            equityStatistics = result.stream().map(item -> {
                EquityStatisticsItemDTO itemDTO = new EquityStatisticsItemDTO();
                itemDTO.setSendCount(item.getSendCount());
                itemDTO.setEquityDTO(this.convertDTO(item.getEquity()));
                // 填充券或券包的属性，并返回总统计数量
                // update 中途版本活动id 由 sendBizFrom 存值发生改变迁移到 sendBizKey 中
                Long detailTotal = fillOtherInfo(itemDTO, item.getSendBizKey());
                totalCount.addAndGet(detailTotal);
                return itemDTO;
            }).collect(Collectors.toList());
            equityStatisticsGroupDTO.setTotalCount(totalCount.get());
        }
        equityStatisticsGroupDTO.setEquityStatistics(equityStatistics);
        return equityStatisticsGroupDTO;

    }

    /**
     * 填充其他信息，并返回券发放数量
     *
     * @param itemDTO
     * @param bizFrom
     */
    private Long fillOtherInfo(EquityStatisticsItemDTO itemDTO, String bizFrom) {
        if (itemDTO == null) {
            return 0L;
        }
        if (itemDTO.getEquityDTO() == null) {
            return itemDTO.getSendCount();
        }
        // 如果是券权益，则补充核销信息
        String activityId = bizFrom;
        String prizeId = String.valueOf(itemDTO.getEquityDTO().getEquityId());
        if (itemDTO.getEquityDTO().getEquityType().equals(EquityTypeEnums.COUPON.name())) {
            String ticketId =
                    itemDTO.getEquityDTO().getPropsInfo().getCouponInfo().getTicketId();
            CouponStatisticsDTO couponStatisticsDTO = getCouponStatistics(activityId, prizeId, ticketId);
            itemDTO.setCouponStatisticsDTO(couponStatisticsDTO);
            return couponStatisticsDTO.getSendCount();
        } else if (itemDTO.getEquityDTO().getEquityType().equals(EquityTypeEnums.COUPONS.name())) {
            String ticketPackageId =
                    itemDTO.getEquityDTO().getPropsInfo().getCouponPackageInfo().getTicketPackageId();
            if (StringUtils.isEmpty(ticketPackageId)) {
                return itemDTO.getSendCount();
            }
            // 根据券包ticketPackageId查询券明细
            List<String> ticketIds = userTicketClientFacade.getTicketInfoList(ticketPackageId);
            if (CollectionUtils.isEmpty(ticketIds)) {
                return itemDTO.getSendCount();
            }
            List<CouponStatisticsDTO> couponStatisticsDTOList =
                    ticketIds.stream().map(o -> getCouponStatistics(activityId, prizeId,
                            o)).collect(Collectors.toList());
            itemDTO.setCouponStatisticsDTOList(couponStatisticsDTOList);
            return couponStatisticsDTOList.stream().map(CouponStatisticsDTO::getSendCount).reduce(Long::sum).get();
        }
        return itemDTO.getSendCount();
    }

    public CouponStatisticsDTO getCouponStatistics(String activityId, String prizeId, String ticketId) {
        TicketInfoStatistics ticketInfoStatistics = userTicketClientFacade.queryTicketUsed(ListTicketBillParam
                .builder().activityId(activityId).prizeId(prizeId).ticketId(ticketId).build());
        CouponStatisticsDTO couponStatisticsDTO = new CouponStatisticsDTO();
        couponStatisticsDTO.setTicketId(ticketId);
        couponStatisticsDTO.setUsedCount(0L);
        couponStatisticsDTO.setSendCount(0L);
        if (ticketInfoStatistics != null) {
            couponStatisticsDTO.setSendCount(ticketInfoStatistics.getSendCount());
            couponStatisticsDTO.setUsedCount(ticketInfoStatistics.getUsedCount());
        }
        return couponStatisticsDTO;
    }

    /**
     * 发送完权益展示权益明细
     *
     * @param bo
     * @return
     */
    public ResponseBaseVo<SendEquityResult> convertSendResult(SendEquityResultBO bo) {
        SendEquityResult sendEquityResult = new SendEquityResult();
        if (bo != null) {
            sendEquityResult.setEquityId(bo.getEquityId());
            sendEquityResult.setEquityName(bo.getEquityName());
            sendEquityResult.setEquityType(bo.getEquityType());
            //权益展示
            produceEquity(bo.getExtendInfo(), sendEquityResult);
        }
        ResponseBaseVo<SendEquityResult> result = ResponseBaseVo.ok(sendEquityResult);
        return result;
    }

    private void produceEquity(ExtendInfo extendInfo, SendEquityResult sendEquityResult) {
        if (extendInfo == null) {
            return;
        }
        EquityAbility target = promotionEquityStore.findSuitable(EquityTypeEnums.get(sendEquityResult.getEquityType()));
        sendEquityResult.setSendResultDetail(target.buildSendResultDetail(extendInfo));
    }

    /**
     * 获取券类型权益展示
     *
     * @param userId
     * @param userTicketId
     * @return
     */
    public UserTicketEquityDTO getCouponSendResult(String userId, String userTicketId) {
        log.info("userTicketClientFacade.getUserTicketInfo-userTicketId:{},userId:{}", userTicketId, userId);
        UserTicketDTO userTicketDTO = userTicketClientFacade.getUserTicketInfo(userTicketId, userId);
        if (userTicketDTO == null) {
            log.info("userTicketClientFacade.getUserTicketInfo-res:null");
            return null;
        }
        return convertUserTicketDtoToEquity(userTicketDTO);
    }

    /**
     * 获取券包类型展示
     *
     * @param userId
     * @param userTicketIds
     * @return
     */
    public List<UserTicketEquityDTO> getUserTicketInfoList(List<String> userTicketIds, String userId) {
        if (CollectionUtils.isEmpty(userTicketIds)) {
            return null;
        }
        List<UserTicketDTO> userTicketDTOs = userTicketClientFacade.getUserTicketInfoList(userTicketIds, userId);
        if (CollectionUtils.isEmpty(userTicketDTOs)) {
            return null;
        }
        return userTicketDTOs.stream().map(EquityControllerConvert::convertUserTicketDto2Equity).collect(Collectors.toList());
    }

    public List<UserGoodsEquityDTO> queryUserGoodsByIds(List<Long> userGoodsIds) {
        List<UserProductBenefitModel> userProductBenefitModels =
                userActivityClientFacade.queryUserActivityByIds(userGoodsIds);
        if (CollectionUtils.isEmpty(userProductBenefitModels)) {
            return null;
        }
        return userProductBenefitModels.stream().map(EquityControllerConvert::convertUserGoodsDtoToEquity).collect(Collectors.toList());
    }

    /**
     * 调用接口返回的dto转换权益系统dto - 新增
     *
     * @param userTicketDTO
     * @return
     */
    public static UserTicketEquityDTO convertUserTicketDto2Equity(UserTicketDTO userTicketDTO) {
        UserTicketEquityDTO userTicketEquityDTO = new UserTicketEquityDTO();
        userTicketEquityDTO.setId(userTicketDTO.getId());
        userTicketEquityDTO.setTicket(userTicketDTO.getTicket());
        if (userTicketDTO.getCoupon_ticket() != null) {
            userTicketEquityDTO.setType(userTicketDTO.getCoupon_ticket().getType());
            userTicketEquityDTO.setName(userTicketDTO.getCoupon_ticket().getName());
            userTicketEquityDTO.setDescription(userTicketDTO.getCoupon_ticket().getDescription());
            userTicketEquityDTO.setDiscount(userTicketDTO.getCoupon_ticket().getDiscount());
        }
        if (userTicketDTO.getExpire_time() != null) {
            userTicketEquityDTO.setExpire_time(userTicketDTO.getExpire_time() * 1000);
        }
        if (userTicketDTO.getStart_time() != null) {
            userTicketEquityDTO.setStart_time(userTicketDTO.getStart_time() * 1000);
        }
        userTicketEquityDTO.setMoney(userTicketDTO.getMoney());
        userTicketEquityDTO.setPay_min(userTicketDTO.getPay_min());
        userTicketEquityDTO.setStatus(userTicketDTO.getStatus());
        userTicketEquityDTO.setUse_time(userTicketDTO.getUse_time());
        userTicketEquityDTO.setIs_invisible(userTicketDTO.getIs_invisible());

        //填充字段
        TicketEquityDTO target = new TicketEquityDTO();
        if (Objects.nonNull(userTicketDTO.getCoupon_ticket())) {
            BeanUtil.copyProperties(userTicketDTO.getCoupon_ticket(), target);
        }

        userTicketEquityDTO.setCoupon_ticket(target);
        userTicketEquityDTO.setScene(userTicketDTO.getScene());
        userTicketEquityDTO.setUse_scene(userTicketDTO.getUse_scene());
        userTicketEquityDTO.setIs_new_user_ticket(userTicketDTO.getIs_new_user_ticket());
        userTicketEquityDTO.setUser(userTicketDTO.getUser());
        userTicketEquityDTO.setCreate_time(userTicketDTO.getCreate_time());
        userTicketEquityDTO.setUpdate_time(userTicketDTO.getUpdate_time());
        userTicketEquityDTO.setVipTicket(userTicketDTO.isVipTicket());
        return userTicketEquityDTO;
    }

    /**
     * 调用接口返回的dto转换权益系统dto
     *
     * @param userTicketDTO
     * @return
     */
    public static UserTicketEquityDTO convertUserTicketDtoToEquity(UserTicketDTO userTicketDTO) {
        UserTicketEquityDTO userTicketEquityDTO = new UserTicketEquityDTO();
        userTicketEquityDTO.setId(userTicketDTO.getId());
        userTicketEquityDTO.setTicket(userTicketDTO.getTicket());
        if (userTicketDTO.getCoupon_ticket() != null) {
            userTicketEquityDTO.setType(userTicketDTO.getCoupon_ticket().getType());
            userTicketEquityDTO.setName(userTicketDTO.getCoupon_ticket().getName());
            userTicketEquityDTO.setDescription(userTicketDTO.getCoupon_ticket().getDescription());
            userTicketEquityDTO.setDiscount(userTicketDTO.getCoupon_ticket().getDiscount());
        }
        if (userTicketDTO.getExpire_time() != null) {
            userTicketEquityDTO.setExpire_time(userTicketDTO.getExpire_time() * 1000);
        }
        if (userTicketDTO.getStart_time() != null) {
            userTicketEquityDTO.setStart_time(userTicketDTO.getStart_time() * 1000);
        }
        userTicketEquityDTO.setMoney(userTicketDTO.getMoney());
        userTicketEquityDTO.setPay_min(userTicketDTO.getPay_min());
        userTicketEquityDTO.setStatus(userTicketDTO.getStatus());
        userTicketEquityDTO.setUse_time(userTicketDTO.getUse_time());
        userTicketEquityDTO.setIs_invisible(userTicketDTO.getIs_invisible());

        return userTicketEquityDTO;
    }

    public static UserGoodsEquityDTO convertUserGoodsDtoToEquity(UserProductBenefitModel userProductBenefitModel) {
        UserGoodsEquityDTO userGoodsEquityDTO = new UserGoodsEquityDTO();
        userGoodsEquityDTO.setUserId(userProductBenefitModel.getUserId());
        userGoodsEquityDTO.setAreaIds(userProductBenefitModel.getAreaIds());
        userGoodsEquityDTO.setId(userProductBenefitModel.getId());
        userGoodsEquityDTO.setBenefits(userProductBenefitModel.getBenefits());
        userGoodsEquityDTO.setType(userProductBenefitModel.getType());
        userGoodsEquityDTO.setStatus(userProductBenefitModel.getStatus());
//        EquityStatus status = EquityStatus.getByGoodsStatus(userProductBenefitModel.getStatus());
//        if (status != null) {
//            userGoodsEquityDTO.setStatus(status.getStatus());
//        }
        userGoodsEquityDTO.setConditions(userProductBenefitModel.getConditions());
        userGoodsEquityDTO.setBeginTime(userProductBenefitModel.getBeginTime());
        userGoodsEquityDTO.setEndTime(userProductBenefitModel.getEndTime());
        userGoodsEquityDTO.setLimit(userProductBenefitModel.getLimit());
        userGoodsEquityDTO.setProductId(userProductBenefitModel.getProductId());
        userGoodsEquityDTO.setProductOid(userProductBenefitModel.getProductOid());
        userGoodsEquityDTO.setSource(userProductBenefitModel.getSource());
        userGoodsEquityDTO.setSourceId(userProductBenefitModel.getSourceId());
        return userGoodsEquityDTO;
    }

    public MorePageResultDTO<SendEquityResult> convertMorePageResult(MorePageResult<EquitySendCommandDO> records) {
        if (records == null || CollectionUtils.isEmpty(records.getRows())) {
            return MorePageResultDTO.empty();
        }
        MorePageResultDTO<SendEquityResult> result = new MorePageResultDTO<>();
        result.setHashMore(records.getHasMore());

        result.setRows(records.getRows().stream().filter(item -> item.getPromotionEquityDO() != null).map(item -> {
            SendEquityResult itemResult = new SendEquityResult();
            itemResult.setEquityId(item.getPromotionEquityDO().getEquityId());
            itemResult.setEquityType(item.getPromotionEquityDO().getEquityType().name());
            itemResult.setEquityName(item.getPromotionEquityDO().getEquityName());
            produceEquity(item.getExtendInfo(), itemResult);
            return itemResult;
        }).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(records.getRows())) {
            result.setNextKey(records.getRows().get(records.getRows().size() - 1).getId());
        }
        return result;
    }

    public SaveOrUpdateEquityRequest convertToRequest(EquityDTO equityDTO) {
        if (equityDTO == null) {
            return null;
        }
        SaveOrUpdateEquityRequest request = new SaveOrUpdateEquityRequest();
        request.setEquityId(equityDTO.getEquityId());
        request.setEquityType(equityDTO.getEquityType());
        request.setEquityName(equityDTO.getEquityName());
        request.setBizKey(equityDTO.getBizKey());
        request.setDescription(equityDTO.getDescription());
        request.setCreateTime(equityDTO.getCreateTime());
        request.setPublishTime(equityDTO.getPublishTime());
        request.setUpdateTime(equityDTO.getUpdateTime());
        request.setStatus(equityDTO.getStatus());
        request.setPropsInfo(equityDTO.getPropsInfo());
        return request;
    }
}
