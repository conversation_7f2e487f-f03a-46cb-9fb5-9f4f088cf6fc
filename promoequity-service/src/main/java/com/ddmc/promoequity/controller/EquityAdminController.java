package com.ddmc.promoequity.controller;

import cn.hutool.core.lang.Assert;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.gateway.bg.client.annotation.HttpApiGetMapping;
import com.ddmc.gateway.bg.client.annotation.HttpApiGroup;
import com.ddmc.gateway.bg.client.annotation.HttpApiPostMapping;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.controller.convert.EquityControllerConvert;
import com.ddmc.promoequity.controller.validator.EquityControllerValidator;
import com.ddmc.promoequity.domain.ablity.entity.SelectLabel;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquitySendStatisticsDO;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.service.EquityDomainService;
import com.ddmc.promoequity.dto.EquityDTO;
import com.ddmc.promoequity.dto.EquityRecordBizCountStatisticDTO;
import com.ddmc.promoequity.dto.EquityStatisticsGroupDTO;
import com.ddmc.promoequity.dto.PageDTO;
import com.ddmc.promoequity.dto.SelectLabelDTO;
import com.ddmc.promoequity.dto.UserEquityResult;
import com.ddmc.promoequity.dto.request.QueryActivityEquityListRequest;
import com.ddmc.promoequity.dto.request.QueryEquityLabelRequest;
import com.ddmc.promoequity.dto.request.QueryEquityListRequest;
import com.ddmc.promoequity.dto.request.QueryEquitysRequest;
import com.ddmc.promoequity.dto.request.QueryStatisticsRequest;
import com.ddmc.promoequity.dto.request.QueryUserEquityRequest;
import com.ddmc.promoequity.dto.request.SaveOrUpdateEquityRequest;
import com.ddmc.promoequity.dto.request.UpdateEquityStateRequest;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.vo.BalanceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权益管理controller
 * 需要做权限控制
 */
@Slf4j
@HttpApiGroup("权益管理")
@RestController
@RequestMapping(value = "/admin/equity")
public class EquityAdminController {

    @Resource
    private EquityDomainService equityDomainService;

    @Resource
    private EquityControllerValidator equityControllerValidator;

    @Resource
    private EquityControllerConvert equityControllerConvert;

    @HttpApiPostMapping(value = "/queryList", name = "查询权益列表")
    public ResponseBaseVo<PageDTO<EquityDTO>> queryList(@RequestBody QueryEquityListRequest queryEquityListRequest) {
        try {
            equityControllerValidator.validatorQueryList(queryEquityListRequest);

            PromotionEquityQuery query = PromotionEquityQuery
                    .builder()
                    .equityTypeEnums(EquityTypeEnums.get(queryEquityListRequest.getEquityType()))
                    .keywords(queryEquityListRequest.getKeywords())
                    .bizKey(queryEquityListRequest.getBizKey())
                    .bizStatus(BizStatus.get(queryEquityListRequest.getStatus()))
                    .build();
            PageResult<PromotionEquityDO> pageResult = equityDomainService.queryList(query,
                    new PageInfo(queryEquityListRequest.getPageNum(), queryEquityListRequest.getPageSize()));

            PageDTO<EquityDTO> result = equityControllerConvert.convertList(pageResult);
            return ResponseBaseVo.ok(result);
        } catch (Exception e) {
            log.error("[查询权益列表]-接口出错,request:{}", queryEquityListRequest, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/queryActiveList", name = "查询可用权益列表")
    public ResponseBaseVo<PageDTO<EquityDTO>> queryActiveList(@RequestBody QueryActivityEquityListRequest queryActivityEquityListRequest) {
        try {
            QueryEquityListRequest request = new QueryEquityListRequest();
            request.setPageNum(1);
            request.setPageSize(100);
            request.setStatus(BizStatus.PUBLISH.name());
            request.setKeywords(queryActivityEquityListRequest.getKeywords());
            request.setEquityType(queryActivityEquityListRequest.getEquityType());
            request.setBizKey(queryActivityEquityListRequest.getBizKey());
            return queryList(request);
        } catch (Exception e) {
            log.error("[查询权益列表]-接口出错,request:{}", queryActivityEquityListRequest, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiGetMapping(value = "/detail", name = "查询权益详细")
    public ResponseBaseVo<EquityDTO> detail(@RequestParam("equityId") Long equityId) {
        try {
            PromotionEquityDO promotionEquityDO = equityDomainService.queryById(equityId);
            return ResponseBaseVo.ok(equityControllerConvert.convertDTO(promotionEquityDO, true));
        } catch (Exception e) {
            log.error("[查询权益详细]-接口出错,equityId:{}，error:{}", equityId, e.getMessage());
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/listDetail", name = "查询权益详细列表")
    public ResponseBaseVo<List<EquityDTO>> listDetail(@RequestBody QueryEquitysRequest queryEquitysRequest) {
        try {
            List<PromotionEquityDO> promotionEquityDOs =
                    equityDomainService.getEquitiesFromCache(queryEquitysRequest.getEquityIds());
            return ResponseBaseVo.ok(equityControllerConvert.convertDTO(promotionEquityDOs, true));
        } catch (Exception e) {
            log.error("[查询权益详细]-接口出错,queryEquitysRequest:{}，error:{}", queryEquitysRequest, e.getMessage());
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    /*
     * 待下线
     */
    @Deprecated
    @HttpApiPostMapping(value = "/saveOrUpdate", name = "更新或保存未发布的权益")
    public ResponseBaseVo<EquityDTO> saveOrUpdate(@RequestBody EquityDTO equityDTO) {
        SaveOrUpdateEquityRequest request = equityControllerConvert.convertToRequest(equityDTO);
        //直接调用下面的
        return saveOrUpdateV1(request);
    }

    @HttpApiPostMapping(value = "/saveOrUpdateV1", name = "更新或保存未发布的权益v1")
    public ResponseBaseVo<EquityDTO> saveOrUpdateV1(@RequestBody SaveOrUpdateEquityRequest request) {
        try {
            equityControllerValidator.validatorSaveOrUpdate(request);
            PromotionEquityDO promotionEquityDO = equityControllerConvert.convertDo(request);
            if (promotionEquityDO.getEquityId() == null) {
                equityDomainService.createEquity(promotionEquityDO);
            } else {
                equityDomainService.updateEquity(promotionEquityDO);
            }
            PromotionEquityDO result = equityDomainService.queryById(promotionEquityDO.getEquityId());
            return ResponseBaseVo.ok(equityControllerConvert.convertDTO(result, request.getShowExtends() == null ?
                    false :
                    request.getShowExtends().booleanValue()));
        } catch (Exception e) {
            log.error("[更新修改权益]-接口出错,equityDTO:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }


    @HttpApiPostMapping(value = "/publish", name = "发布权益")
    public ResponseBaseVo publish(@RequestBody UpdateEquityStateRequest request) {
        try {
            Assert.notNull(request.getEquityId(), "权益ID不能为空");
            equityDomainService.publishEquity(request.getEquityId());
            return ResponseBaseVo.ok();
        } catch (Exception e) {
            log.error("[发布权益]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/abandon", name = "废弃权益")
    public ResponseBaseVo abandon(@RequestBody UpdateEquityStateRequest request) {
        try {
            Assert.notNull(request.getEquityId(), "权益ID不能为空");
            equityDomainService.abandonEquity(request.getEquityId());
            return ResponseBaseVo.ok();
        } catch (Exception e) {
            log.error("[废弃权益]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/queryTagList", name = "查询权益配置")
    public ResponseBaseVo<List<SelectLabelDTO>> queryTagList(@RequestBody QueryEquityLabelRequest request) {
        try {
            Assert.notNull(request.getEquityType(), "权益类别不能为空");
            List<SelectLabel> result = equityDomainService.queryTagList(EquityTypeEnums.get(request.getEquityType()),
                    request.getKeywords());
            return ResponseBaseVo.ok(equityControllerConvert.convertSelectLabel(result));
        } catch (Exception e) {
            log.error("[查询权益配置]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/equityStatistics", name = "权益发放统计")
    public ResponseBaseVo<EquityStatisticsGroupDTO> equityStatistics(@RequestBody QueryStatisticsRequest request) {
        try {
            List<PromotionEquitySendStatisticsDO> result =
                    equityDomainService.equityStatistics(request.getSendBizFrom(), request.getSendBizKey(), request.getEquityTypeEnumsList());
            EquityStatisticsGroupDTO equityStatisticsGroupDTO = equityControllerConvert.convertStatistics(result);
            return ResponseBaseVo.ok(equityStatisticsGroupDTO);
        } catch (Exception e) {
            log.error("[权益统计出错]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/bizCountRecordStatistics", name = "权益发放统计")
    public ResponseBaseVo<EquityRecordBizCountStatisticDTO> bizCountRecordStatistics(@RequestBody EquityRecordBizCountStatisticDTO request) {
        try {
            EquityRecordBizCountStatisticDTO result =
                    equityDomainService.bizCountRecordStatistics(request);
            return ResponseBaseVo.ok(result);
        } catch (Exception e) {
            log.error("[权益统计出错]-接口出错,request:{}", request, e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiGetMapping(value = "/queryBalanceList", name = "余额列表查询")
    public ResponseBaseVo<List<BalanceInfo>> queryBalanceList() {
        try {
            List<BalanceInfo> result =
                    equityDomainService.queryBalanceList();
            return ResponseBaseVo.ok(result);
        } catch (Exception e) {
            log.error("[余额列表查询]-接口出错!", e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }

    @HttpApiPostMapping(value = "/queryUserEquityInfo", name = "查询用户权益")
    ResponseBaseVo<List<UserEquityResult>> queryUserEquityInfo(@RequestBody List<QueryUserEquityRequest> request) {
        try {
            List<UserEquityResult> resultList = equityDomainService.queryUserEquityInfo(request);
            return ResponseBaseVo.ok(resultList);
        } catch (Exception e) {
            log.error("[用户权益详情]-接口出错!", e);
            return ResponseBaseVo.fail(500, e.getMessage());
        }
    }
}
