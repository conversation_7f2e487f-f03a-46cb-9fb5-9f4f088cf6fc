package com.ddmc.promoequity.controller.validator;

import cn.hutool.core.lang.Assert;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.dto.request.QueryEquityListRequest;
import com.ddmc.promoequity.dto.request.SaveOrUpdateEquityRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


@Component
public class EquityControllerValidator {
    public void validatorQueryList(QueryEquityListRequest queryEquityListRequest) {
        Assert.notNull(queryEquityListRequest, "查询参数不能为空");
        Assert.notNull(queryEquityListRequest.getPageNum(), "页码[pageIndex]不能为空");
        Assert.isTrue(queryEquityListRequest.getPageNum() > 0, "页码[pageIndex]必须大于0");
        Assert.notNull(queryEquityListRequest.getPageSize(), "页容量[pageSize]不能为空");
        Assert.isTrue(queryEquityListRequest.getPageSize() > 0, "页容量[pageSize]必须大于0");
        if (!StringUtils.isEmpty(queryEquityListRequest.getStatus())) {
            Assert.isTrue(BizStatus.get(queryEquityListRequest.getStatus()) != null, "status 必须为 NEW,PUBLISH,ABANDON " +
                    "中的一个");
        }
    }

    public void validatorSaveOrUpdate(SaveOrUpdateEquityRequest request) {
        if (request.getEquityId() == null) {
            //验证新增
            Assert.notEmpty(request.getEquityType(), "权益类别不能为空");
            Assert.notEmpty(request.getEquityName(), "权益名称不能为空");
        } else {
            //验证修改
        }
    }
}
