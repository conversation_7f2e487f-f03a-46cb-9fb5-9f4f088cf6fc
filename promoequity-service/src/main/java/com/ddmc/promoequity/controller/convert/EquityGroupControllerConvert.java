package com.ddmc.promoequity.controller.convert;

import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.dto.EquityGroupDTO;
import com.ddmc.promoequity.dto.PageDTO;
import com.ddmc.promoequity.enums.BizLineEnums;
import com.ddmc.promoequity.enums.UseSceneEnums;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.stream.Collectors;

@Component
public class EquityGroupControllerConvert {

    @Resource
    private EquityControllerConvert equityControllerConvert;

    public PageDTO<EquityGroupDTO> convertList(PageResult<PromotionEquityGroupDO> pageResult) {
        if (pageResult == null) {
            return null;
        }
        PageDTO<EquityGroupDTO> result = new PageDTO<>();
        result.setCount(pageResult.getCount());
        result.setPageSize(pageResult.getSize());
        result.setTotalPage(pageResult.getTotal());
        result.setIndex(pageResult.getIndex());
        if (pageResult.getResult() != null) {
            result.setRows(pageResult.getResult().stream().map(item -> {
                EquityGroupDTO temp = new EquityGroupDTO();
                temp.setGroupId(item.getGroupId());
                temp.setGroupName(item.getGroupName());
                temp.setDescription(item.getDescription());
                if (item.getBizLine() != null) {
                    temp.setBizLine(item.getBizLine().name());
                }
                if (item.getUseScene() != null) {
                    temp.setUseScene(item.getUseScene().name());
                }
                temp.setCreator(item.getCreator());
                if (item.getCreateTime() != null) {
                    temp.setCreateTime(item.getCreateTime().getTime());
                }
                if (item.getUpdateTime() != null) {
                    temp.setUpdateTime(item.getUpdateTime().getTime());
                }
                if (item.getPublishTime() != null) {
                    temp.setPublishTime(item.getPublishTime().getTime());
                }
                if (item.getStatus() != null) {
                    temp.setStatus(item.getStatus().name());
                }
                if (item.getEquityList() != null) {
                    temp.setEquityList(item.getEquityList()
                            .stream().map(equityControllerConvert::convertDTO).collect(Collectors.toList()));
                }
                return temp;
            }).collect(Collectors.toList()));
        }

        return result;
    }

    public EquityGroupDTO convertDO(PromotionEquityGroupDO item) {
        if (item == null) {
            return null;
        }
        EquityGroupDTO temp = new EquityGroupDTO();
        temp.setGroupId(item.getGroupId());
        temp.setGroupName(item.getGroupName());
        temp.setDescription(item.getDescription());
        if (item.getBizLine() != null) {
            temp.setBizLine(item.getBizLine().name());
        }
        if (item.getUseScene() != null) {
            temp.setUseScene(item.getUseScene().name());
        }
        temp.setCreator(item.getCreator());
        if (item.getCreateTime() != null) {
            temp.setCreateTime(item.getCreateTime().getTime());
        }
        if (item.getUpdateTime() != null) {
            temp.setUpdateTime(item.getUpdateTime().getTime());
        }
        if (item.getPublishTime() != null) {
            temp.setPublishTime(item.getPublishTime().getTime());
        }
        if (item.getStatus() != null) {
            temp.setStatus(item.getStatus().name());
        }
        if (item.getEquityList() != null) {
            temp.setEquityList(item.getEquityList()
                    .stream().map(equityControllerConvert::convertDTO).collect(Collectors.toList()));
        }
        return temp;
    }

    public PromotionEquityGroupDO convertDTO(EquityGroupDTO equityDTO) {
        if (equityDTO == null) {
            return null;
        }
        PromotionEquityGroupDO promotionEquityGroupDO = new PromotionEquityGroupDO();
        promotionEquityGroupDO.setGroupId(equityDTO.getGroupId());
        promotionEquityGroupDO.setGroupName(equityDTO.getGroupName());
        promotionEquityGroupDO.setDescription(equityDTO.getDescription());
        if (equityDTO.getBizLine() != null) {
            promotionEquityGroupDO.setBizLine(BizLineEnums.get(equityDTO.getBizLine()));
        }
        promotionEquityGroupDO.setUseScene(UseSceneEnums.get(equityDTO.getUseScene()));
        promotionEquityGroupDO.setCreator(equityDTO.getCreator());
        if (equityDTO.getCreateTime() != null) {
            promotionEquityGroupDO.setCreateTime(new Date(equityDTO.getCreateTime()));
        }
        if (equityDTO.getUpdateTime() != null) {
            promotionEquityGroupDO.setUpdateTime(new Date(equityDTO.getUpdateTime()));
        }
        if (equityDTO.getPublishTime() != null) {
            promotionEquityGroupDO.setPublishTime(new Date(equityDTO.getPublishTime()));
        }
        if (!CollectionUtils.isEmpty(equityDTO.getEquityIdList())) {
            promotionEquityGroupDO.setEquityList(
                    equityDTO.getEquityIdList().stream().map(item -> {
                        PromotionEquityDO equityDO = new PromotionEquityDO();
                        equityDO.setEquityId(item);
                        return equityDO;
                    }).collect(Collectors.toList())
            );
        }
        return promotionEquityGroupDO;
    }
}
