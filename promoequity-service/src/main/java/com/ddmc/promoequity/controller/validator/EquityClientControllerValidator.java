package com.ddmc.promoequity.controller.validator;

import com.ddmc.promoequity.dto.request.SendRecordRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


@Component
public class EquityClientControllerValidator {
    public void validator(SendRecordRequest request) {
        if (StringUtils.isEmpty(request.getBizFrom())) {
            throw new IllegalArgumentException("bizFrom 不能为空");
        }
        if (StringUtils.isEmpty(request.getUid())) {
            throw new IllegalArgumentException("uid 不能为空");
        }
        if (request.getPreKey() == null || request.getPreKey() < 0) {
            throw new IllegalArgumentException("preKey必须为大于等于0的整数");
        }
        if (request.getSize() == null || request.getSize() <= 0) {
            throw new IllegalArgumentException("size必须大于0");
        }
    }
}
