package com.ddmc.promoequity.utils;

import com.alibaba.fastjson.JSON;
import com.csoss.monitor.api.cat.Message;
import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.metrics.Counter;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.StatusCode;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.AttributeKeys;
import lombok.extern.slf4j.Slf4j;

/**
 * 埋点工具
 */
@Slf4j
public class CatUtils {


    /**
     * 计数指标 - trace记录携带数据
     */
    public static void logEvent(String type, String name, String status, Object data) {
        try {
            Span span = Traces.spanBuilder(type, Instrumentation.EVENT_NEW).startSpan();
            Metrics.newCounter(type, Instrumentation.EVENT_NEW).build().once(Attributes.of(AttributeKey.stringKey("name"), name));
            span.metricsMapping(type, Attributes.of(AttributeKey.stringKey("name"), name,
                    AttributeKey.stringKey(AttributeKeys.METRIC_STATUS), status));
            if (data != null) {
                span.addEvent("event_data", Attributes.of(AttributeKey.stringKey("data"), JSON.toJSONString(data)));
            }
            span.end();
        } catch (Exception e) {
            log.error("CatUtils logEvent has exception,auto fallback", e);
        }
    }

    public static void logEvent(String type, String name, Object data) {
        try {
            logEvent(type, name, Message.SUCCESS, data);
        } catch (Exception e) {
            log.error("打点异常", e);
        }
    }

    public static void logEvent(String type, String name) {
        try {
            logEvent(type, name, Message.SUCCESS, null);
        } catch (Exception e) {
            log.error("logEvent exception", e);
        }
    }

    public static void logCount(long count, String name, String attribute, String value) {
        Counter counter = Metrics.newCounter(name).build();
        counter.add(count, Attributes.of(AttributeKey.stringKey(attribute), value));
    }

    public static void logError(String message, Throwable cause) {
        Traces.spanBuilder(cause.getClass().getSuperclass().getSimpleName())
                .setAttribute("message", message)
                .startSpan()
                .recordException(cause)
                .setStatus(StatusCode.ERROR)
                .end();
    }
}
