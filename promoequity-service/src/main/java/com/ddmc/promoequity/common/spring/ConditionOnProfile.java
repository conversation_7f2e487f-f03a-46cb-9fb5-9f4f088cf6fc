package com.ddmc.promoequity.common.spring;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Map;

public class ConditionOnProfile implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {

        Map<String, Object> datas = metadata.getAnnotationAttributes(ExcludeProfile.class.getName());
        if (CollectionUtils.isEmpty(datas) || datas.get("value") == null) {
            return true;
        }
        String[] profiles = (String[]) datas.get("value");
        if (profiles.length == 0) {
            return true;
        }
        return Arrays.stream(context.getEnvironment().getActiveProfiles()).noneMatch(item ->
                Arrays.asList(profiles).contains(item));
    }

}
