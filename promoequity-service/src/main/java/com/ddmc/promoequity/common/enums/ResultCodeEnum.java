package com.ddmc.promoequity.common.enums;

import com.ddmc.promoequity.common.exception.ServiceException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Getter
public enum ResultCodeEnum {
    SUCCESS(true, 0, "执行成功"),

    IDEMPOTENT(true, 199_000_00, "幂等"),

    SYSTEM_EXCEPTION(false, 199_000_10, "系统错误"),

    SENDTICKET_FAIL(false, 9001, "发券失败"),

    SENDTICKET_UNKOWN(false, 9009, "发券结果未知"),

    SEND_EQUITY_FAIL(false, 1001, "发送权益失败"),

    SEND_VIP_FAIL(false, 2001, "兑换会员失败"),

    ;

    private boolean success;

    private Integer code;

    private String message;

    ResultCodeEnum(boolean success, Integer code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }


    public ServiceException toServiceException(String message) {
        return new ServiceException(getCode(), message);
    }

    public ServiceException toServiceException() {
        return new ServiceException(getCode(), null);
    }
}
