package com.ddmc.promoequity.common.exception;


import com.ddmc.core.view.compat.ResponseBaseVo;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/15
 */
@ToString
public class ServiceResult<T> implements Serializable {

    private static final long serialVersionUID = 0L;

    private boolean success;

    private Integer code;

    private String message;

    private T data;

    private Object errorData;

    private ServiceResult subResult;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Object getErrorData() {
        return errorData;
    }

    public void setErrorData(Object errorData) {
        this.errorData = errorData;
    }

    public ServiceResult getSubResult() {
        return subResult;
    }

    public void setSubResult(ServiceResult subResult) {
        this.subResult = subResult;
    }

    public ServiceException toServiceException(String message) {
        if (success) {
            throw new IllegalStateException("can't convert ServiceResult to ServiceException when the filed value of " +
                    "success is true");
        } else {
            ServiceException exception = new ServiceException(code, this.message + " " + message, subResult);
            exception.setData(data);
            exception.setErrorData(errorData);
            return exception;
        }
    }

    public ServiceException toServiceException() {
        if (success) {
            throw new IllegalStateException("can't convert ServiceResult to ServiceException when the filed value of " +
                    "success is true");
        } else {
            ServiceException exception = new ServiceException(code, message, subResult);
            exception.setData(data);
            exception.setErrorData(errorData);
            return exception;
        }
    }

    public ResponseBaseVo toResponseBaseVo() {
        ResponseBaseVo vo = new ResponseBaseVo();
        vo.setSuccess(success);
        vo.setCode(code);
        vo.setMsg(message);
        vo.setData(data);
        vo.setTimestamp(String.valueOf(System.currentTimeMillis()));
        return vo;
    }

    public ServiceResult toServiceResult() {
        ServiceResult serviceResult = new ServiceResult();
        serviceResult.setCode(this.getCode());
        serviceResult.setMessage(this.getMessage());
        serviceResult.setSuccess(this.isSuccess());
        return serviceResult;
    }

}
