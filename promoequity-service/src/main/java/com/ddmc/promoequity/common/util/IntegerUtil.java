package com.ddmc.promoequity.common.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * @description: Integer工具类
 */
public class IntegerUtil {

    public static Boolean isTrue(Integer num) {
        if (num == null || num == 0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static int getIntValueSafety(String value) {
        if (StringUtils.isNotBlank(value)) {
            return Integer.parseInt(value);
        }
        return 0;
    }

    public static int getIntValueSafety4FloatStr(String value) {
        if (StringUtils.isNotBlank(value)) {
            return new BigDecimal(value).intValue();
        }
        return 0;
    }

    public static int getIntValueSafety(Object value) {
        if (Objects.nonNull(value)) {
            if (value instanceof Integer) {
                return (int) value;
            } else if (value instanceof String) {
                return Integer.parseInt((String) value);
            }
        }
        return 0;
    }

}