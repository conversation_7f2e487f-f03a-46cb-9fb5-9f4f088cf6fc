package com.ddmc.promoequity.common.enums;

import lombok.Getter;

public interface CommonEnum {

    @Getter
    enum SWITCH {
        /**
         * 开
         */
        ON("on", "开"),
        /**
         * 关
         */
        OFF("off", "关");

        private String val;
        private String msg;

        SWITCH(String val, String msg) {
            this.val = val;
            this.msg = msg;
        }
    }

    @Getter
    enum INTEGER_BOOL {
        /**
         * 是
         */
        YES(1, "是"),
        /**
         * 否
         */
        NO(0, "否");

        private Integer code;
        private String msg;

        INTEGER_BOOL(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

}