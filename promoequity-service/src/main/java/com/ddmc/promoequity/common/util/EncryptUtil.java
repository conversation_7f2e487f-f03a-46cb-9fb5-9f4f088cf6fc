package com.ddmc.promoequity.common.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 *  加解密工具类
 */
public class EncryptUtil {

  private static final String IV = "87328C7A9A671C8C";

  private static final String SN_KEY = "E377DFD83EE16826";

  private static final String CIPHER_METHOD = "AES/GCM/NoPadding";

  private static final String CHARSET = "utf-8";

  private static Logger logger = LoggerFactory.getLogger(EncryptUtil.class);


  public static String decryptDeviceInfo(String fingerPrint) {
    try {
      Cipher cipher = Cipher.getInstance(CIPHER_METHOD);

      SecretKeySpec keySpec = new SecretKeySpec(SN_KEY.getBytes(CHARSET), "AES");
      IvParameterSpec secretIv = new IvParameterSpec(IV.getBytes(CHARSET));

      cipher.init(Cipher.DECRYPT_MODE, keySpec, secretIv);
      byte[] output = cipher.doFinal(new Base64().decode(fingerPrint));
      return new String(output, CHARSET);
    } catch (Exception e) {
      logger.error("decryptDeviceInfo fail,fingerPrint:{}", fingerPrint, e);
    }

    return StringUtils.EMPTY;

  }


}
