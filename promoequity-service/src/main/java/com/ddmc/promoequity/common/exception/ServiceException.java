package com.ddmc.promoequity.common.exception;


/**
 * <AUTHOR>
 * @date 2020/8/7
 */
public class ServiceException extends RuntimeException {

    private Integer code;

    private ServiceResult subResult;

    private Object data;

    private Object errorData;

    public ServiceException(Integer code, String message) {
        super(message);
        this.code = code;
        this.subResult = null;
    }

    public ServiceException(Integer code, String message, ServiceResult subResult) {
        super(message);
        this.code = code;
        this.subResult = subResult;
    }

    public ServiceException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.subResult = null;
    }

    public Integer getCode() {
        return code;
    }

    public ServiceResult getSubResult() {
        return subResult;
    }

    public ServiceResult toServiceResult() {
        ServiceResult result = new ServiceResult();
        result.setSuccess(false);
        result.setCode(code);
        result.setMessage(getMessage());
        result.setSubResult(subResult);
        result.setData(data);
        result.setErrorData(errorData);
        return result;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public void setErrorData(Object errorData) {
        this.errorData = errorData;
    }
}

