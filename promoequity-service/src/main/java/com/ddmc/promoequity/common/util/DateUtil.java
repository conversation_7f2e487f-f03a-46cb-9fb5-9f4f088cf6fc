package com.ddmc.promoequity.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class DateUtil {

    private static final BigDecimal THOUSAND = new BigDecimal("1000");

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDD = "yyyyMMdd";

    public static DateTimeFormatter ymd = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static DateTimeFormatter ym = DateTimeFormatter.ofPattern("yyyyMM");

    public static final DateTimeFormatter dayDf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    private static ThreadLocal<DateFormat> yyyyMMdd = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMdd");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMM = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyyMM");
        }
    };

    private static ThreadLocal<DateFormat> MMdd = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("MM月dd日");
        }
    };

    private static ThreadLocal<DateFormat> yyyy = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMMddHHmmAcross = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMMddHHmmssAcross = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMMddConstAcross = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMMddAcross = new ThreadLocal() {

        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };

    public static String format_yyyyMMddConst(Date date) {
        return yyyyMMddConstAcross.get().format(date);
    }

    public static String format_yyyyMMdd(Date date) {
        return yyyyMMdd.get().format(date);
    }

    public static String format_MMdd(Date date) {
        if (Objects.nonNull(date)) {
            return MMdd.get().format(date);
        }
        return Strings.EMPTY;
    }

    public static Date parse_yyyyMMdd(String date) {
        try {
            return yyyyMMdd.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static String format_yyyyMM(Date date) {
        return yyyyMM.get().format(date);
    }

    public static String format_yyyyMMddAcross(Date date) {
        return yyyyMMddAcross.get().format(date);
    }

    public static Date parse_yyyyMMddAcross(String date) {
        try {
            return yyyyMMddAcross.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static String format_yyyyMMddHHmmAcross(Long millis) {
        return yyyyMMddHHmmAcross.get().format(millis);
    }

    public static String format_yyyyMMddHHmmAcrossBySeconds(Long seconds) {
        return format_yyyyMMddHHmmAcross(seconds * 1000);
    }

    public static Date parse_yyyyMMddHHmmAcross(String date) {
        try {
            return yyyyMMddHHmmAcross.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static Date parse_yyyyMMddConstAcross(String date) {
        try {
            return yyyyMMddConstAcross.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static String format_yyyyMMddHHmmssAcross(Date date) {
        return yyyyMMddHHmmssAcross.get().format(date);
    }


    public static Date parse_yyyyMMddHHmmssAcross(String date) {
        try {
            return yyyyMMddHHmmssAcross.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static Date parse_yyyyMM(String date) {
        try {
            return yyyyMM.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static LocalDate convertLocalDate(Long seconds) {
        return new Date(seconds * 1000).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static String format_yyyy(Date date) {
        return yyyy.get().format(date);
    }

    public static Date parse_yyyy(String date) {
        try {
            return yyyy.get().parse(date);
        } catch (ParseException e) {
            throw new RuntimeException("时间解析异常:" + date, e);
        }
    }

    public static Date truncatedToDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Long getCurrentTimeSeconds() {
        return System.currentTimeMillis() / 1000;
    }

    public static String formatDate(Date date, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    public static String formatDate(Long seconds, String pattern) {
        return formatDate(new Date(seconds * 1000L), pattern);
    }

    /**
     * 判断给定时间是否在当前时间后
     * 给定时间格式：07：00
     *
     * @param time
     * @return
     */
    public static boolean isTimeAfterNow(String time) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            return LocalTime.now().isBefore(LocalTime.parse(time, formatter));
        } catch (Exception e) {
            log.error("DateUtil.isTimeAfterNow ERROR: {}", e);
            return false;
        }
    }

    /**
     * 判断给定时间是否在当前时间前
     * 时间格式：23：00
     *
     * @param time
     * @return
     */
    public static boolean isTimeBeforeNow(String time) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            return LocalTime.now().isAfter(LocalTime.parse(time, formatter));
        } catch (Exception e) {
            log.error("DateUtil.isTimeBeforeNow ERROR: {}", e);
            return false;
        }
    }

    /**
     * 当前时间
     *
     * @return date
     */
    public static Date currentDate() {
        return new Date();
    }

    public static long distanceDayEndSecond(LocalDateTime now) {
        if (Objects.isNull(now)) {
            return 0;
        }

        LocalDateTime endDay = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 23, 59, 59);
        return Duration.between(now, endDay).getSeconds();
    }

    public static Integer getNowToTodayEndTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long seconds = ChronoUnit.SECONDS.between(now, midnight);
        return (int) seconds;
    }

    public static String getExecutionTime(LocalDateTime start) {

        Long t1 = start.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        Long t2 = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        //时间差
        Long distance = t2 - t1;
        //取毫秒小数形式
        BigDecimal value = new BigDecimal(distance).divide(THOUSAND);
        return value.setScale(4, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static String getLogTime() {

        Long time = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        //取毫秒小数形式
        BigDecimal value = new BigDecimal(time).divide(THOUSAND);
        return value.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static int getTodayWeek() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_WEEK) - 1;
    }

    public static void main(String[] args) {
        convertLocalDate(1614587400L);
        distanceDayEndSecond(LocalDateTime.now());
        getLogTime();
        System.out.println(getTodayWeek());
    }
}
