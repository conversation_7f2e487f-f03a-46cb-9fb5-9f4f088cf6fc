package com.ddmc.promoequity.common.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Date 2021/10/28
 */
public class SignUtil {

    /**
     * 开放平台的签名验证
     */
    public static String signForOpen(TreeMap<String, String> param) {
        StringBuilder sb = new StringBuilder();
        param.forEach((k, v) -> {
            if (k != null && v != null) {
                sb.append(k).append('=').append(v).append('&');
            }
        });
        sb.deleteCharAt(sb.length() - 1);
        return DigestUtils.md5Hex(sb.toString());
    }

    public static String signForBalance(String sign, TreeMap<String, Object> param) {
        if (param != null && !param.isEmpty()) {
            param = sortByKey(param);
            StringBuilder sb = new StringBuilder(sign);
            param.forEach((k, v) -> sb.append(k).append('=').append(v).append('&'));
            sb.deleteCharAt(sb.length() - 1);
            return DigestUtils.md5Hex(sb.toString());
        } else {
            return DigestUtils.md5Hex(sign);
        }
    }

    public static String riskSign(String sign, TreeMap<String, Object> param) {
        if (param != null && !param.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            param.forEach((k, v) -> sb.append(k).append('=').append(v).append('&'));
            sb.deleteCharAt(sb.length() - 1);
            sb.append(sign);
            return DigestUtils.md5Hex(sb.toString());
        } else {
            return DigestUtils.md5Hex(sign);
        }
    }

    /**
     * 按k排序
     *
     * @param map
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K extends Comparable<? super K>, V> TreeMap<K, V> sortByKey(TreeMap<K, V> map) {
        TreeMap<K, V> result = new TreeMap<>();
        map.entrySet().stream().sorted(
                        Map.Entry.<K, V>comparingByKey().reversed())
                .forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        return result;
    }

    public static void main(String[] args) {
        TreeMap<String, Object> param = new TreeMap<>();
        param.put("channel_id", 1);
        param.put("card_id", 3);
        param.put("serial_num", 4);
        param.put("uid", 6);
        param.put("recharge_source", 1);
        param = sortByKey(param);
        param.forEach((k, v) -> System.out.println("key:" + k + ",value:" + v));
        StringBuffer sb = new StringBuffer("");
        param.forEach((k, v) -> sb.append(k).append('=').append(v).append('&'));
        System.out.println(sb.deleteCharAt(sb.length() - 1));
    }

    public static String weChatSign(String sign, TreeMap<String, Object> param) {
        if (param == null || param.isEmpty()) {
            return null;
        }
        param.put("appkey", sign);
        StringBuilder sb = new StringBuilder();
        param.forEach((k, v) -> sb.append(k).append('=').append(v).append('&'));
        sb.deleteCharAt(sb.length() - 1);
        String md5Value = DigestUtils.md5Hex(sb.toString());
        param.remove("appkey");
        return md5Value;
    }
}
