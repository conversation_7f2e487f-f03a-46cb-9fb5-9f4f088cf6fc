package com.ddmc.promoequity.common.util;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.google.common.collect.Lists.newArrayList;
import static org.apache.commons.lang.StringUtils.isBlank;

/**
 * 正则工具类
 **/
public class RegexUtil {

    public static final String NUMBER = "[0-9|.]*";

    public static List<String> matchGroups(String regex, String param) {
        List<String> res = newArrayList();
        if (isBlank(regex) || isBlank(param)) {
            return res;
        }
        final Matcher matcher = Pattern.compile(regex).matcher(param);
        while (matcher.find()) {
            res.add(matcher.group());
        }
        return res;
    }

    public static String reverseDigital(String param) {
        if (isBlank(param)) {
            return param;
        }
        return param.replaceAll("[\u4e00-\u9fa5]|[a-zA-Z]|[*]", "");
    }

    public static boolean matchDigital(String param) {
        if (isBlank(param)) {
            return false;
        }
        return Pattern.matches(NUMBER, param);
    }

}
