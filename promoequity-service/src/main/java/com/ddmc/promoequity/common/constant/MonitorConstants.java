package com.ddmc.promoequity.common.constant;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/4 19:51
 * @description
 */
public class MonitorConstants {

    public interface EquityCacheManager {
        /**
         * 本地缓存穿透到 DB
         */
        String LOCAL_CACHE_PENETRATION_TO_DB = "local_cache_penetration_to_db";
        /**
         * 加载权益信息（本地缓存 load）
         */
        String LOAD_EQUITY = "load_equity";
        /**
         * 获取权益信息（从本地缓存获取）
         */
        String GET_EQUITY_FROM_CACHE = "get_equity_from_cache";
        /**
         * 批量获取权益信息（从本地缓存获取）
         */
        String GET_EQUITIES_FROM_CACHE = "get_equities_from_cache";
    }
}
