package com.ddmc.promoequity.common.util;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ddmc.promoequity.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * 基于 httpclient 4.3版本的 HttpClient工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {

    private static CloseableHttpClient createSSLClientDefault() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {

                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
            SSLConnectionSocketFactory connSocketFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
            return HttpClients.custom().setSSLSocketFactory(connSocketFactory).build();
        } catch (Throwable e) {
            log.error("[创建ssl]异常", e);
        }
        return HttpClients.createDefault();
    }

    public static String doPostByAppForm(String url, Map<String, Object> params) {
        CloseableHttpClient client = null;
        try {
            HttpPost method = new HttpPost(url);
            List<NameValuePair> listNVP = new ArrayList<>();
            if (params != null) {
                for (String key : params.keySet()) {
                    listNVP.add(new BasicNameValuePair(key, String.valueOf(params.get(key))));
                }
            }
            method.addHeader("Content-type", "application/x-www-form-urlencoded");
            method.setEntity(new UrlEncodedFormEntity(listNVP, "UTF-8"));
            client = createSSLClientDefault();
            HttpResponse resultRsp = client.execute(method);
            int code = resultRsp.getStatusLine().getStatusCode();
            if (code == 200) {
                return EntityUtils.toString(resultRsp.getEntity());
            } else {
                throw new ServiceException(code, "http post请求失败: code=" + code + " ,url=" + url + " ,param=" + JSONUtil.toJsonStr(params));
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(client);
        }
    }

    public static String doPostByAppForm(String url, Map<String, Object> params, Map<String, Object> headMap) {
        CloseableHttpClient client = null;
        try {
            HttpPost method = new HttpPost(url);
            List<NameValuePair> listNVP = new ArrayList<>();
            if (params != null) {
                for (String key : params.keySet()) {
                    listNVP.add(new BasicNameValuePair(key, String.valueOf(params.get(key))));
                }
            }
            method.addHeader("Content-type", "application/x-www-form-urlencoded");
            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    method.addHeader(key, String.valueOf(headMap.get(key)));
                }
            }
            method.setEntity(new UrlEncodedFormEntity(listNVP, "UTF-8"));
            client = createSSLClientDefault();
            HttpResponse resultRsp = client.execute(method);
            int code = resultRsp.getStatusLine().getStatusCode();
            if (code == 200) {
                return EntityUtils.toString(resultRsp.getEntity());
            } else {
                throw new ServiceException(code, "http post请求失败: code=" + code + " ,url=" + url + " ,param=" + JSONUtil.toJsonStr(params));
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(client);
        }
    }

    public static String doPostByAppJSON(String url, Map<String, Object> params) {
        CloseableHttpClient client = null;
        try {
            HttpPost method = new HttpPost(url);
            StringEntity requestEntity = new StringEntity(JSONUtil.toJsonStr(params), ContentType.APPLICATION_JSON);
            method.setEntity(requestEntity);
            client = createSSLClientDefault();
            HttpResponse resultRsp = client.execute(method);
            int code = resultRsp.getStatusLine().getStatusCode();
            if (code == 200) {
                return EntityUtils.toString(resultRsp.getEntity());
            } else {
                throw new ServiceException(code, "http post请求失败: code=" + code + " ,url=" + url + " ,param=" + JSONUtil.toJsonStr(params));
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(client);
        }
    }

    public static String doGet(String url, Map<String, Object> param) {
        CloseableHttpClient client = null;
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            if (null != param) {
                List<NameValuePair> list = new LinkedList<>();
                for (String key : param.keySet()) {
                    list.add(new BasicNameValuePair(key, String.valueOf(param.get(key))));
                }
                uriBuilder.setParameters(list);
            }
            HttpGet method = new HttpGet(uriBuilder.build());
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(5000)  //连接超时
                    .setConnectionRequestTimeout(1000) //从连接池中获取超时
                    .setSocketTimeout(3000) //读取超时
                    .build();
            method.setConfig(requestConfig);
            client = createSSLClientDefault();
            HttpResponse resultRsp = client.execute(method);
            int code = resultRsp.getStatusLine().getStatusCode();
            if (code == 200) {
                return EntityUtils.toString(resultRsp.getEntity());
            } else {
                throw new ServiceException(code, "http get请求失败:" + JSON.toJSONString(resultRsp));
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(client);
        }
    }
}

