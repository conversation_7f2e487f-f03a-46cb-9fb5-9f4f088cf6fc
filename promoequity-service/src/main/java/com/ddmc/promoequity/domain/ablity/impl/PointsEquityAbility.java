package com.ddmc.promoequity.domain.ablity.impl;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.PointClientFacade;
import com.ddmc.promoequity.infra.facade.entity.PointsOptionParam;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PointInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.userpoint.api.enums.FromTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 积分权益
 *
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Slf4j
@Component
public class PointsEquityAbility extends AbstractEquityAbility {
    @Resource
    private PointClientFacade pointClientFacade;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.POINTS == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            PointsOptionParam pointsOptionParam = createPointsOptionParam(command);
            ResponseBaseVo<Boolean> responseBaseVo = pointClientFacade.increasePoint(pointsOptionParam);
            if (responseBaseVo.isSuccess() && responseBaseVo.getData() != null && responseBaseVo.getData().booleanValue()) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                success.setExtendInfo(new ExtendInfo(command.getPropsInfo().getPointInfo()));
                return success;
            } else {
                return SendEquityResultBO.fail(responseBaseVo.getMsg());
            }
        } catch (Exception e) {
            log.error("[积分权益] 发放失败 request:{}", command, e);
            return SendEquityResultBO.fail(e.getMessage());
        }
    }

    private PointsOptionParam createPointsOptionParam(EquitySendCommandDO command) {
        PointsOptionParam pointsOptionParam = new PointsOptionParam();
        Integer point = command.getPropsInfo().getPointInfo().getPoint();
        if (point == null) {
            throw new IllegalArgumentException("缺少积分数量，commandId:" + command.getCommandId());
        }
        pointsOptionParam.setUserId(command.getUid());
        pointsOptionParam.setRequestId(command.getBizNo());
        pointsOptionParam.setDescription(command.getPropsInfo().getPointInfo().getDescription());
        pointsOptionParam.setPoint(command.getPropsInfo().getPointInfo().getPoint());
        pointsOptionParam.setType(FromTypeEnum.USER_SIGN_DRAW_LOTTERY_PRIZE.getCode());
        pointsOptionParam.setOrderNumber(command.getBizNo());
        return pointsOptionParam;
    }

    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        PointInfo pointInfo =
                Optional.ofNullable(propsInfo.getPointInfo()).orElse(new PointInfo());
        pointInfo.setDescription(equitySendDO.getDescription());
        propsInfo.setPointInfo(pointInfo);
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo){
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setPointInfo(extendInfo.getPointInfo());
        return sendResultDetail;
    }
}
