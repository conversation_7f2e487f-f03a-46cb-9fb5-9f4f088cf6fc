package com.ddmc.promoequity.domain.service;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityGroupQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityGroupRepository;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.infra.convert.EquityGroupConvert;
import com.ddmc.promoequity.infra.factory.EquityGroupFactory;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroupItem;
import com.ddmc.promoequity.infra.validator.EquityGroupValidator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EquityGroupDomainService {

    @Resource
    private EquityGroupConvert equityGroupConvert;

    @Resource
    private EquityGroupFactory equityGroupFactory;

    @Resource
    private EquityGroupValidator equityGroupValidator;

    @Resource
    private PromotionEquityGroupRepository groupRepository;

    @Resource
    private PromotionEquityRepository promotionEquityRepository;

    @Transactional(rollbackFor = Exception.class)
    public void createEquityGroup(PromotionEquityGroupDO promotionEquityGroupDO) {
        //验证参数
        equityGroupValidator.validatorCreate(promotionEquityGroupDO);
        //创建组
        TbPromotionEquityGroup group = equityGroupConvert.convertCreate(promotionEquityGroupDO);
        //创建组
        groupRepository.insertOrUpdate(group);
        //回填主键
        promotionEquityGroupDO.setGroupId(group.getGroupId());

        List<TbPromotionEquity> equityList =
                equityGroupConvert.convertItemCreate(promotionEquityGroupDO.getEquityList());
        //保存关联关系
        groupRepository.addEquityItem(group.getGroupId(), equityList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEquityGroup(PromotionEquityGroupDO groupDO) {
        //验证参数
        equityGroupValidator.validatorUpdate(groupDO);
        //构造组
        TbPromotionEquityGroup group = equityGroupConvert.convertUpdate(groupDO);
        //更新组
        groupRepository.insertOrUpdate(group);

        if (!StringUtils.isEmpty(groupDO.getEquityList())) {
            //清空关联关系
            groupRepository.clearEquityItem(groupDO.getGroupId());
            List<TbPromotionEquity> equityList = equityGroupConvert.convertItemCreate(groupDO.getEquityList());
            //保存关联关系
            groupRepository.addEquityItem(group.getGroupId(), equityList);
        }
    }


    public PromotionEquityGroupDO queryEquityGroup(Long groupId) {
        if (groupId == null) {
            return null;
        }
        TbPromotionEquityGroup group = groupRepository.findById(groupId);
        if (group == null) {
            return null;
        }
        List<TbPromotionEquity> equityList = Lists.newArrayList();
        List<TbPromotionEquityGroupItem> item = groupRepository.queryGroupItem(groupId);
        if (!CollectionUtils.isEmpty(item)) {
            equityList = promotionEquityRepository.selectByIds(item
                    .stream().map(TbPromotionEquityGroupItem::getEquityId).collect(Collectors.toList()));
        }
        return equityGroupFactory.createGroupDO(group, equityList);
    }

    public PageResult<PromotionEquityGroupDO> queryList(PromotionEquityGroupQuery query, PageInfo pageInfo) {
        PageResult<TbPromotionEquityGroup> promotionGroupPage = groupRepository.queryList(query, pageInfo);
        if (promotionGroupPage == null) {
            return null;
        }
        PageResult<PromotionEquityGroupDO> result = new PageResult<>();
        result.setIndex(promotionGroupPage.getIndex());
        result.setSize(promotionGroupPage.getSize());
        result.setTotal(promotionGroupPage.getTotal());
        result.setCount(promotionGroupPage.getCount());
        if (!CollectionUtils.isEmpty(promotionGroupPage.getResult())) {
            result.setResult(promotionGroupPage.getResult().stream()
                    .map(item -> equityGroupFactory.createGroupDO(item)).collect(Collectors.toList()));
        }
        return result;
    }

    public void publish(Long groupId) {
        long count = groupRepository.updateStatus(groupId, BizStatus.NEW, BizStatus.PUBLISH);
        if (count <= 0) {
            throw new IllegalStateException("发布失败，只有新建状态的权益组能够发布");
        }
    }

    public void abandon(Long groupId) {
        long count = groupRepository.updateStatus(groupId, BizStatus.PUBLISH, BizStatus.ABANDON);
        if (count <= 0) {
            throw new IllegalStateException("废弃失败，只有已发布的权益组能够废弃");
        }
    }


}
