package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.jdbc.pool.util.JsonUtils;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.*;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.UserStepFacade;
import com.ddmc.promoequity.infra.facade.entity.IncreaseVirtualStepsParam;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.VirtualStepInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 虚拟步数权益
 * @class: VirtualStepEquityAbility
 * @date: 2022/12/26 15:47
 * @author: wangfan
 */
@Component
@Slf4j
public class VirtualStepEquityAbility extends AbstractEquityAbility {

    @Autowired
    private UserStepFacade userStepFacade;
    /**
     * 判断是否能处理
     *
     * @param equityTypeEnums
     */
    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.VIRTUAL_STEP == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            IncreaseVirtualStepsParam param = new IncreaseVirtualStepsParam();
            param.setUid(command.getUid());
            param.setStep(command.getPropsInfo().getVirtualStepInfo().getAmount());
            param.setActivityId(command.getPropsInfo().getActivityId());
            param.setBizFrom(command.getBizFrom());
            param.setBizNo(command.getBizNo());
            ResponseBaseVo<Void> responseBaseVo = userStepFacade.increaseVirtualSteps(param);
            if (responseBaseVo.isSuccess()) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                VirtualStepInfo virtualStepInfo = new VirtualStepInfo();
                virtualStepInfo.setAmount(command.getPropsInfo().getVirtualStepInfo().getAmount());
                success.setExtendInfo(new ExtendInfo(virtualStepInfo));
                return success;
            } else {
                return SendEquityResultBO.fail(responseBaseVo.getMsg());
            }
        } catch (Exception e) {
            log.error("[虚拟步数] 发放失败 request:{}", JSONUtil.toJsonStr(command), e);
            return SendEquityResultBO.fail(e.getMessage());
        }
    }

    @Override
    public List<SelectLabel> label(QueryLabel queryLabel) {
        return super.label(queryLabel);
    }


    /**
     * 创建权益的时候验证
     *
     * @param promotionEquityDO
     */
    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO.getPropsInfo(), "虚拟步数不可为空");
        Assert.notNull(promotionEquityDO.getPropsInfo().getVirtualStepInfo(), "虚拟步数不可为空");
        Assert.isTrue(promotionEquityDO.getPropsInfo().getVirtualStepInfo().getAmount() > 0, "虚拟步数须大于0");
        return ValidateEquityResultBO.success();
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        return super.buildSendResultDetail(extendInfo);
    }
}
