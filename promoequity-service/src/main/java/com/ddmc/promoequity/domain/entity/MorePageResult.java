package com.ddmc.promoequity.domain.entity;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class MorePageResult<T> {
    private List<T> rows;
    private Boolean hasMore;

    public static MorePageResult empty() {
        MorePageResult empty = new MorePageResult();
        empty.setHasMore(false);
        empty.setRows(Lists.newArrayList());
        return empty;
    }
}
