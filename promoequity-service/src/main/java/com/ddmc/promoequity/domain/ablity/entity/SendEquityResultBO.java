package com.ddmc.promoequity.domain.ablity.entity;

import com.ddmc.promoequity.vo.ExtendInfo;
import lombok.Data;

@Data
public class SendEquityResultBO {
    private boolean success;
    private String msg;
    private Integer errorCode = 500;
    private Long equityId;
    private String equityName;
    private String equityType;
    private ExtendInfo extendInfo;

    public static SendEquityResultBO success(String msg) {
        SendEquityResultBO result = new SendEquityResultBO();
        result.setSuccess(true);
        result.setMsg(msg);
        return result;
    }

    public static SendEquityResultBO success() {

        return success(null);
    }

    public static SendEquityResultBO fail() {
        return fail(null);
    }

    public static SendEquityResultBO fail(String msg) {
        SendEquityResultBO result = new SendEquityResultBO();
        result.setSuccess(false);
        result.setMsg(msg);
        return result;
    }
    public static SendEquityResultBO fail(Integer errorCode , String msg) {
        SendEquityResultBO result = new SendEquityResultBO();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setMsg(msg);
        return result;
    }
}
