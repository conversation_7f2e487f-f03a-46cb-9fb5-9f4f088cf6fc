package com.ddmc.promoequity.domain.ablity;

import com.ddmc.promoequity.domain.ablity.entity.*;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 权益能力，
 * 用来接入下游的权益
 */
public interface EquityAbility {
    /**
     * 发放权益
     */
    SendEquityResultBO sendEquity(PromotionEquityDO promotionEquityDO, EquitySendDO equitySendDO);

    /**
     * 重试
     */
    SendEquityResultBO retry(EquitySendCommandDO equitySendCommandDO);

    default List<SelectLabel> label(QueryLabel queryLabel) {
        return Lists.newArrayList();
    }

    /**
     * 发送权益的时候验证
     */
    default ValidateEquityResultBO validate(EquitySendDO equitySendDO) {
        return ValidateEquityResultBO.success();
    }

    /**
     * 创建权益的时候验证
     */
    default ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        return ValidateEquityResultBO.success();
    }

    default SendResultDetail buildSendResultDetail(ExtendInfo extendInfo){
        return new SendResultDetail();
    }

}
