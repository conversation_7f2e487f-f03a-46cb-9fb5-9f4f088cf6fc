package com.ddmc.promoequity.domain.ablity.impl;

import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.vo.ExtendInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 空奖权益
 *
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Slf4j
@Component
public class EmptyEquityAbility extends AbstractEquityAbility {
    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.EMPTY == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        SendEquityResultBO success = SendEquityResultBO.success();
        success.setEquityId(command.getPromotionEquityDO().getEquityId());
        success.setEquityName(command.getPromotionEquityDO().getEquityName());
        success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
        success.setExtendInfo(new ExtendInfo());
        return success;
    }
}
