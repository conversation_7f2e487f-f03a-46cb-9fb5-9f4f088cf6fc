package com.ddmc.promoequity.domain.service;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.infra.facade.PointClientFacade;
import com.ddmc.promoequity.infra.facade.entity.PointsOptionParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @Date 2022/05/12
 */
@Component
@Slf4j
public class EquityPointService {

    @Resource
    private PointClientFacade pointClientFacade;

    /**
     * 增加积分
     * @param param
     * @return
     */
    public ResponseBaseVo<Boolean> increasePoint(PointsOptionParam param) {
        return pointClientFacade.increasePoint(param);
    }

    /**
     * 扣减积分
     * @param param
     * @return
     */
    public ResponseBaseVo<Boolean> decreasePoint(PointsOptionParam param) {
        return pointClientFacade.decreasePoint(param);
    }

    /**
     * 查询用户总积分
     * @param param
     * @return
     */
    public ResponseBaseVo<Integer> getUserTotalPoint(String userId) {
        return pointClientFacade.getUserTotalPoint(userId);
    }

    /**
     * 奖励积分
     * @param param
     * @return
     */
    public ResponseBaseVo<Boolean> awardPoint(PointsOptionParam param) {
        return pointClientFacade.awardPoint(param);
    }

    /**
     * 积分兑换
     * @param param
     * @return
     */
    public ResponseBaseVo<Boolean> pointExchange(PointsOptionParam param) {
        return pointClientFacade.pointExchange(param);
    }

}
