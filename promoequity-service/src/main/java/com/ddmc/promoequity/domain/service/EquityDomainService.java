package com.ddmc.promoequity.domain.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.controller.convert.EquityControllerConvert;
import com.ddmc.promoequity.domain.ablity.PromotionEquityStore;
import com.ddmc.promoequity.domain.ablity.entity.BalanceResult;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.QueryLabel;
import com.ddmc.promoequity.domain.ablity.entity.SelectLabel;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.entity.MorePageResult;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquitySendStatisticsDO;
import com.ddmc.promoequity.domain.entity.vo.EquityCommandListQuery;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionSendCommandRepository;
import com.ddmc.promoequity.dto.EquityRecordBizCountStatisticDTO;
import com.ddmc.promoequity.dto.UserEquityResult;
import com.ddmc.promoequity.dto.UserGoodsEquityDTO;
import com.ddmc.promoequity.dto.UserTicketEquityDTO;
import com.ddmc.promoequity.dto.request.QueryUserEquityRequest;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.cache.EquityCacheManager;
import com.ddmc.promoequity.infra.convert.BalanceInfoConvert;
import com.ddmc.promoequity.infra.convert.PromotionEquityConvert;
import com.ddmc.promoequity.infra.facade.BalanceClientFacade;
import com.ddmc.promoequity.infra.factory.EquitySendCommandFactory;
import com.ddmc.promoequity.infra.factory.PromotionEquityFactory;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.validator.PromotionEquityValidator;
import com.ddmc.promoequity.vo.BalanceInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EquityDomainService {

    @Resource
    private PromotionEquityRepository promotionEquityRepository;

    @Resource
    private PromotionEquityValidator promotionEquityValidator;

    @Resource
    private PromotionEquityFactory promotionEquityFactory;

    @Resource
    private EquitySendCommandFactory equitySendCommandFactory;

    @Resource
    private PromotionEquityConvert promotionEquityConvert;

    @Resource
    private PromotionEquityStore promotionEquityStore;

    @Resource
    private PromotionSendCommandRepository promotionSendCommandRepository;
    @Resource
    private BalanceClientFacade balanceClientFacade;
    @Resource
    private BalanceInfoConvert balanceInfoConvert;
    @Resource
    private EquityControllerConvert equityControllerConvert;
    @Resource
    private EquityCacheManager equityCacheManager;

    /**
     * 创建权益
     */
    public void createEquity(PromotionEquityDO promotionEquityDO) {
        promotionEquityValidator.validateCreate(promotionEquityDO);
        TbPromotionEquity tbPromotionEquity = promotionEquityConvert.convertCreatePromotionEquity(promotionEquityDO);
        promotionEquityRepository.insertOrUpdate(tbPromotionEquity);
        promotionEquityDO.setEquityId(tbPromotionEquity.getEquityId());
    }

    /**
     * 查询权益信息。直接走 DB，仅供 B 端使用
     *
     * @param equityId 权益 ID
     * @return 权益信息
     */
    public PromotionEquityDO queryById(Long equityId) {
        if (equityId == null) {
            return null;
        }
        TbPromotionEquity tbPromotionEquity = promotionEquityRepository.findById(equityId);
        return promotionEquityFactory.createEquity(tbPromotionEquity);
    }

    /**
     * 查询权益信息。走本地缓存，C 端使用
     *
     * @param equityId 权益 ID
     * @return 权益信息
     */
    public PromotionEquityDO getEquityFromCache(Long equityId) {
        if (equityId == null || equityId <= 0) {
            log.error("getEquityFromCache param error equityId={}", equityId);
            return null;
        }

        TbPromotionEquity tbPromotionEquity = equityCacheManager.getEquity(equityId);
        return promotionEquityFactory.createEquity(tbPromotionEquity);
    }

    /**
     * 查询权益信息列表。走本地缓存，C 端使用
     *
     * @param equityIds 权益 ID 列表
     * @return 权益信息
     */
    public List<PromotionEquityDO> getEquitiesFromCache(List<Long> equityIds) {
        if (CollectionUtils.isEmpty(equityIds)) {
            log.error("getEquitiesFromCache param error");
            return null;
        }

        List<TbPromotionEquity> tbPromotionEquities = equityCacheManager.getEquities(equityIds);
        return promotionEquityFactory.createEquitys(tbPromotionEquities);
    }

    /**
     * 更新权益
     */
    public void updateEquity(PromotionEquityDO promotionEquityDO) {
        promotionEquityValidator.validateUpdate(promotionEquityDO);
        //补全其他属性
        PromotionEquityDO origin = this.queryById(promotionEquityDO.getEquityId());
        //
        TbPromotionEquity tbPromotionEquity = promotionEquityConvert.convertUpdatePromotionEquity(promotionEquityDO,
                origin);
        promotionEquityRepository.insertOrUpdate(tbPromotionEquity);
    }

    /**
     * 查找权益列表
     */
    public PageResult<PromotionEquityDO> queryList(PromotionEquityQuery query, PageInfo pageInfo) {
        PageResult<TbPromotionEquity> promotionEquity = promotionEquityRepository.queryList(query, pageInfo);
        if (promotionEquity == null) {
            return null;
        }
        PageResult<PromotionEquityDO> result = new PageResult<>();
        result.setIndex(promotionEquity.getIndex());
        result.setSize(promotionEquity.getSize());
        result.setTotal(promotionEquity.getTotal());
        result.setCount(promotionEquity.getCount());
        if (!CollectionUtils.isEmpty(promotionEquity.getResult())) {
            result.setResult(promotionEquity.getResult().stream()
                    .map(item -> promotionEquityFactory.createEquity(item)).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 发布权益,只能从new状态，发布至publish
     */
    public void publishEquity(Long equityId) {
        long count = promotionEquityRepository.updateStatus(equityId, BizStatus.NEW, BizStatus.PUBLISH);
        if (count <= 0) {
            throw new IllegalStateException("发布失败，只有新建状态的权益能够被发布");
        }
    }

    /**
     * 废弃权益
     */
    public void abandonEquity(Long equityId) {
        long count = promotionEquityRepository.updateStatus(equityId, BizStatus.PUBLISH, BizStatus.ABANDON);
        if (count <= 0) {
            throw new IllegalStateException("废弃失败，只有已发布的活动能够废弃");
        }
    }

    /**
     * 发放权益
     *
     * @param equitySendDO
     */
    public SendEquityResultBO sendEquity(EquitySendDO equitySendDO) {
        promotionEquityValidator.validateSend(equitySendDO);
        PromotionEquityDO promotionEquityDO = this.getEquityFromCache(equitySendDO.getEquityId());
        SendEquityResultBO result = promotionEquityStore.sendEquity(promotionEquityDO, equitySendDO);
        return result;
    }

    public List<SelectLabel> queryTagList(EquityTypeEnums equityTypeEnums, String keywords) {
        QueryLabel queryLabel = new QueryLabel();
        queryLabel.setEquityTypeEnums(equityTypeEnums);
        queryLabel.setKeywords(keywords);
        return promotionEquityStore.queryTag(queryLabel);
    }

    public void retrySendEquity(String commandId) {
        promotionEquityStore.retry(commandId);
    }

    public List<PromotionEquitySendStatisticsDO> equityStatistics(String sendBizFrom, String sendBizKey,
                                                                  List<EquityTypeEnums> equityTypeEnums) {
        if (StringUtils.isEmpty(sendBizFrom) && StringUtils.isEmpty(sendBizKey)) {
            return Lists.newArrayList();
        }

        if (StringUtils.isEmpty(sendBizKey)) {
            sendBizKey = sendBizFrom;
        }

        List<TbPromotionEquitySendStatistics> result = promotionEquityRepository.sendStatistics(sendBizFrom, sendBizKey);
        if (CollectionUtils.isEmpty(result)) {
            return Lists.newArrayList();
        }
        return result.stream().map(item -> {
            PromotionEquitySendStatisticsDO promotionEquitySendStatisticsDO = new PromotionEquitySendStatisticsDO();
            promotionEquitySendStatisticsDO.setId(item.getId());
            promotionEquitySendStatisticsDO.setEquity(this.queryById(item.getEquityId()));
            promotionEquitySendStatisticsDO.setCreateTime(item.getCreateTime());
            promotionEquitySendStatisticsDO.setUpdateTime(item.getUpdateTime());
            promotionEquitySendStatisticsDO.setValid(item.getValid());
            promotionEquitySendStatisticsDO.setSendCount(item.getSendCount());
            promotionEquitySendStatisticsDO.setSendBizFrom(item.getSendBizFrom());
            promotionEquitySendStatisticsDO.setSendBizKey(item.getSendBizKey());
            return promotionEquitySendStatisticsDO;
        }).filter(o -> {
            if (CollectionUtils.isEmpty(equityTypeEnums)) {
                return true;
            }
            return equityTypeEnums.contains(o.getEquity().getEquityType());
        }).collect(Collectors.toList());
    }

    public EquityRecordBizCountStatisticDTO bizCountRecordStatistics(EquityRecordBizCountStatisticDTO request) {
        EquityRecordBizCountStatisticDTO result = promotionEquityRepository.bizCountRecordStatistics(request);
        return result;
    }

    public List<BalanceInfo> queryBalanceList() {
        List<BalanceResult> result = balanceClientFacade.queryBalanceList();
        log.info("queryBalanceList::result ={}", JSONUtil.toJsonStr(result));
        return balanceInfoConvert.resultCoverToInfoList(result);
    }

    public List<UserEquityResult> queryUserEquityInfo(List<QueryUserEquityRequest> request) {
        log.info("queryUserEquityInfo req:{}", JSONUtil.toJsonStr(request));
        Assert.notEmpty(request, "查询列表为空");
        // 分离券的ids
        List<String> userTicketIds = request
                .stream()
                .filter(o -> EquityTypeEnums.COUPON == o.getEquityType())
                .map(QueryUserEquityRequest::getUserEquityId)
                .collect(Collectors.toList());
        List<UserTicketEquityDTO> userTicketEquityDTOList = equityControllerConvert.getUserTicketInfoList(userTicketIds,
                null);
        // 分离商品折扣品的ids
        List<Long> userGoodsIds = request
                .stream()
                .filter(o -> EquityTypeEnums.DISCOUNT_GOODS == o.getEquityType()
                        && NumberUtil.isLong(o.getUserEquityId())
                )
                .map(o -> Long.valueOf(o.getUserEquityId()))
                .collect(Collectors.toList());
        List<UserGoodsEquityDTO> userGoodsEquityDTOList = equityControllerConvert.queryUserGoodsByIds(userGoodsIds);
        List<UserEquityResult> userEquityResultList = request.stream().map(o -> {
            UserEquityResult result = new UserEquityResult();
            result.setEquityType(o.getEquityType());
            result.setUserEquityId(o.getUserEquityId());
            if (EquityTypeEnums.COUPON == o.getEquityType() && !CollectionUtils.isEmpty(userTicketEquityDTOList)) {
                result.setUserTicketInfo(userTicketEquityDTOList.stream().filter(x -> x.getId().equals(o.getUserEquityId())).findFirst().orElse(null));
            }
            if (EquityTypeEnums.DISCOUNT_GOODS == o.getEquityType() && !CollectionUtils.isEmpty(userGoodsEquityDTOList) && NumberUtil.isLong(o.getUserEquityId())) {
                result.setUserGoodsEquityDTO(userGoodsEquityDTOList.stream().filter(x -> x.getId().longValue() == Long.parseLong(o.getUserEquityId())).findFirst().orElse(null));
            }
            return result;
        }).collect(Collectors.toList());
        return userEquityResultList;
    }


    public MorePageResult<EquitySendCommandDO> sendEquityRecord(EquityCommandListQuery query) {
        MorePageResult<TbPromotionEquitySendCommand> pageData = promotionSendCommandRepository.findListByPreKey(query);

        MorePageResult<EquitySendCommandDO> result = new MorePageResult<>();
        result.setHasMore(pageData.getHasMore());
        if (!CollectionUtils.isEmpty(pageData.getRows())) {
            result.setRows(pageData.getRows().stream().map(equitySendCommandFactory::createCommandDO).collect(Collectors.toList()));
        }
        return result;
    }
}
