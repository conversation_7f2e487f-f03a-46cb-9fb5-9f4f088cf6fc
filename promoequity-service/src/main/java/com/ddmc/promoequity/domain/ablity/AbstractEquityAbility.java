package com.ddmc.promoequity.domain.ablity;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.csoss.monitor.sdk.resource.Status;
import com.ddmc.promoequity.common.enums.ResultStatusEnums;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.EquityMonitor;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import com.ddmc.promoequity.infra.service.EquitySendCommandStateServiceImpl;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.ddmc.promoequity.infra.facade.constants.TicketConstant.EQUITY_TICKET_SCENE;

/**
 * 权益能力模板类
 * 需要新增权益能力的时候，只需要继承该模板类即可
 */
@Slf4j
public abstract class AbstractEquityAbility implements EquityAbility {

    @Resource
    private EquitySendCommandStateServiceImpl equitySendCommandStateService;

    @Resource
    private EquityMonitor equityMonitor;
    @Resource
    private PromotionEquitySendCommandMapper commandMapper;

    /**
     * 判断是否能处理
     */
    public abstract boolean suit(EquityTypeEnums equityTypeEnums);

    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
    }

    protected abstract SendEquityResultBO doSendEquity(EquitySendCommandDO command);

    /**
     * 构建sendCommand
     *
     * @param promotionEquityDO
     * @param equitySendDO
     * @return
     */
    public EquitySendCommandDO buildSendCommand(PromotionEquityDO promotionEquityDO,
                                                EquitySendDO equitySendDO) {
        validateCommand(promotionEquityDO, equitySendDO);
        EquitySendCommandDO equitySendCommandDO = new EquitySendCommandDO();
        equitySendCommandDO.setPromotionEquityDO(promotionEquityDO);
        equitySendCommandDO.setBizFrom(equitySendDO.getBizFrom());
        equitySendCommandDO.setBizNo(equitySendDO.getBizNo());
        equitySendCommandDO.setUid(equitySendDO.getUid());
        PropsInfo propsInfo = Optional.ofNullable(promotionEquityDO.getPropsInfo()).orElse(new PropsInfo());
        String activityId =
                equitySendDO.getPropsInfo() == null || StringUtils.isBlank(equitySendDO.getPropsInfo().getActivityId()) ?
                        equitySendDO.getBizKey() : equitySendDO.getPropsInfo().getActivityId();
        propsInfo.setActivityId(activityId);
        String stationId =
                equitySendDO.getPropsInfo() == null || StringUtils.isBlank(equitySendDO.getPropsInfo().getStationId()) ?
                        equitySendDO.getStationId() : equitySendDO.getPropsInfo().getStationId();
        propsInfo.setStationId(stationId);
        propsInfo.setScene(equitySendDO.getScene() == null ?
                EQUITY_TICKET_SCENE :
                equitySendDO.getScene());
        propsInfo.setRiskInfo(Objects.isNull(equitySendDO.getPropsInfo()) ? null : equitySendDO.getPropsInfo().getRiskInfo());
        fieldEquityProp(propsInfo, equitySendDO);
        equitySendCommandDO.setPropsInfo(propsInfo);
        return equitySendCommandDO;
    }

    @Override
    public SendEquityResultBO sendEquity(PromotionEquityDO promotionEquityDO, EquitySendDO equitySendDO) {

        EquitySendCommandDO command = buildSendCommand(promotionEquityDO, equitySendDO);
        String commandId = null;
        try {
            TbPromotionEquitySendCommand oldCommand = commandMapper.queryByUnique(command.getBizFrom(),
                    command.getBizNo(),
                    command.getUid());
            if (oldCommand != null) {
                if (ResultStatusEnums.SUCCESS.name().equals(oldCommand.getStatus())) {
                    return buildResultBySuccessCommand(command, oldCommand);
                } else {
                    commandId = oldCommand.getCommandId();
                    log.info("goto sendEquity retry fail,commandId:{}",commandId);
                }
            } else {
                commandId = equitySendCommandStateService.start(command);
            }
            //发送逻辑
            SendEquityResultBO result = doSendEquity(command);
            if (result.isSuccess()) {
                equitySendCommandStateService.success(commandId, result.getExtendInfo());
                equitySendCommandStateService.increaseSendCount(commandId);
                equityMonitor.logEvent("success",command.getPromotionEquityDO().getEquityType().toString(), Status.SUCCESS, command);
            } else {
                if (equitySendDO != null) {
                    log.warn("sendEquity doSendEquity failed. userId={}, equityId={}, bizFrom={}, bizNo={}, commandId={}" +
                                    ", result={}",
                            equitySendDO.getUid(), equitySendDO.getEquityId(), equitySendDO.getBizFrom(), equitySendDO.getBizNo(),
                            commandId, JSONUtil.toJsonStr(result));
                } else {
                    log.warn("sendEquity doSendEquity failed. command={}, result={}",
                            JSONUtil.toJsonStr(command), JSONUtil.toJsonStr(result));
                }
                equitySendCommandStateService.fail(commandId, result.getMsg());
                equityMonitor.logEvent("error",command.getPromotionEquityDO().getEquityType().toString(), Status.SUCCESS, command);
            }
            return result;
        } catch (Exception e) {
            log.error("AbstractEquityAbility,command:{}", command, e);
            if (commandId != null) {
                equitySendCommandStateService.fail(commandId, e.getMessage());
            }
            equityMonitor.logEvent("error",command.getPromotionEquityDO().getEquityType().toString(), Status.SUCCESS, command);
            return SendEquityResultBO.fail(e.getMessage());
        }
    }

    private SendEquityResultBO buildResultBySuccessCommand(EquitySendCommandDO command,
                                                           TbPromotionEquitySendCommand commandDB) {
        SendEquityResultBO success = SendEquityResultBO.success();
        success.setEquityId(command.getPromotionEquityDO().getEquityId());
        success.setEquityName(command.getPromotionEquityDO().getEquityName());
        success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
        success.setExtendInfo(JSONUtil.toBean(commandDB.getExtendsInfo(),ExtendInfo.class ));
        return success;
    }

    @Override
    public SendEquityResultBO retry(EquitySendCommandDO command) {
        validateRetry(command);

        try {
            SendEquityResultBO result = doSendEquity(command);
            if (result.isSuccess()) {
                equitySendCommandStateService.success(command.getCommandId(), result.getExtendInfo());
            } else {
                equitySendCommandStateService.fail(command.getCommandId(), result.getMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("AbstractEquityAbility", e);
            equitySendCommandStateService.fail(command.getCommandId(), e.getMessage());
        }
        SendEquityResultBO fail = new SendEquityResultBO();
        return fail;
    }

    protected void validateRetry(EquitySendCommandDO command) {
        if (command == null) {
            throw new IllegalArgumentException("command cannot be null");
        }
        if (!ResultStatusEnums.FAIL.name().equals(command.getStatus())) {
            throw new IllegalStateException("只有失败状态的命令允许重试");
        }

    }

    /**
     * 验证发送数据完整性
     *
     * @param promotionEquityDO
     * @param equitySendDO
     */
    private void validateCommand(PromotionEquityDO promotionEquityDO,
                                 EquitySendDO equitySendDO) {
        Assert.notNull(promotionEquityDO, "equity cannot be null");
        Assert.isTrue(this.suit(promotionEquityDO.getEquityType()),
                "cannot send equity  of type " + promotionEquityDO.getEquityType());
        Assert.notNull(equitySendDO, "uid 不能为空");
        Assert.notBlank(equitySendDO.getUid(), "uid 不能为空");
        Assert.notBlank(equitySendDO.getBizFrom(), "bizFrom 不能为空");
        Assert.notBlank(equitySendDO.getBizNo(), "bizNo 不能为空");
        // 具体权益类型校验
        this.validate(equitySendDO);
    }
}
