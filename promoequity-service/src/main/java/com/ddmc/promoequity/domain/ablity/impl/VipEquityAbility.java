package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.VipClientFacade;
import com.ddmc.promoequity.utils.CatUtils;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PointExchangeVipInfo;
import com.ddmc.vip.app.response.internal_api.ExchangeVipDaysResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 会员权益
 *
 * <AUTHOR>
 * @date 2023/10/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VipEquityAbility extends AbstractEquityAbility {

    private final VipClientFacade vipClientFacade;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.VIP.equals(equityTypeEnums);
    }

    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO.getPropsInfo(), "缺少兑换会员属性信息");
        Assert.notNull(promotionEquityDO.getPropsInfo().getExchangeVipInfo(), "缺少兑换会员属性信息");
        PointExchangeVipInfo exchangeVipInfo = promotionEquityDO.getPropsInfo().getExchangeVipInfo();
        Assert.notNull(exchangeVipInfo.getSource(), "缺少兑换会员来源信息");
        Assert.notNull(exchangeVipInfo.getExchangeVipDays(), "缺少兑换会员天数信息");
        Assert.notNull(exchangeVipInfo.getNeedPointNum(), "缺少兑换会员消耗积分信息");
        return ValidateEquityResultBO.success();
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        SendEquityResultBO success = null;
        try {
            //发送会员权益
            ExchangeVipDaysResDTO vipDaysResDTO = this.vipClientFacade.exchangeVip(command);
            //
            String message = String.format("uid=%s, bizNo=%s", command.getUid(), command.getBizNo());
            if (null == vipDaysResDTO || StringUtils.isBlank(vipDaysResDTO.getOrderNumber())) {
                CatUtils.logEvent("EXCHANGE_VIP", "FAIL", message);
                return SendEquityResultBO.fail(ResultCodeEnum.SEND_VIP_FAIL.getCode(), ResultCodeEnum.SEND_VIP_FAIL.getMessage());
            }
            CatUtils.logEvent("EXCHANGE_VIP", "SUCCESS", message);
            success = SendEquityResultBO.success();
            success.setEquityId(command.getPromotionEquityDO().getEquityId());
            success.setEquityName(command.getPromotionEquityDO().getEquityName());
            success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
            //反向冗余会员生成的订单号
            PointExchangeVipInfo exchangeVipInfo = command.getPropsInfo().getExchangeVipInfo();
            exchangeVipInfo.setVipOrderNumber(vipDaysResDTO.getOrderNumber());
            success.setExtendInfo(new ExtendInfo(exchangeVipInfo));
        } catch (Exception e) {
            CatUtils.logError("兑换会员权益失败. uid:" + command.getUid(), e);
            return SendEquityResultBO.fail(ResultCodeEnum.SEND_VIP_FAIL.getCode(), ResultCodeEnum.SEND_VIP_FAIL.getMessage());
        }
        return success;
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setExchangeVipInfo(extendInfo.getExchangeVipInfo());
        return sendResultDetail;
    }

}
