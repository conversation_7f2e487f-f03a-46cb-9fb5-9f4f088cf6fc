package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.controller.convert.EquityControllerConvert;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.dto.UserTicketEquityDTO;
import com.ddmc.promoequity.dto.UserTicketPackageEquityDTO;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.UserTicketClientFacade;
import com.ddmc.promoequity.infra.facade.entity.SendTicketParam;
import com.ddmc.promoequity.vo.CouponPackageInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.vouchercore.client.dto.TicketPackageDTO;
import com.ddmc.vouchercore.client.response.SendTicketPackageSyncResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static com.ddmc.promoequity.infra.facade.constants.TicketConstant.EQUITY_TICKET_TYPE;

/**
 * 优惠券包
 *
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Slf4j
@Component
public class CouponsEquityAbility extends AbstractEquityAbility {

    @Resource
    private UserTicketClientFacade userTicketClientFacade;

    @Resource
    private EquityControllerConvert equityControllerConvert;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.COUPONS == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {

        try {
            SendTicketParam sendTicketParam = createDefaultSendTicketParam(command);
            ResponseBaseVo<SendTicketPackageSyncResponse> responseBaseVo =
                    userTicketClientFacade.sendTicketPackage(sendTicketParam);
            if (responseBaseVo.isSuccess()) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());

                if (responseBaseVo.getData() != null) {
                    CouponPackageInfo couponPackageInfo = new CouponPackageInfo();
                    couponPackageInfo.setUserTicketPackageId(responseBaseVo.getData().getUser_ticket_package_id());
                    couponPackageInfo.setUserTicketIds(responseBaseVo.getData().getUser_ticket_ids());
                    couponPackageInfo.setUserId(command.getUid());
                    success.setEquityId(command.getPromotionEquityDO().getEquityId());
                    success.setEquityName(command.getPromotionEquityDO().getEquityName());
                    success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                    success.setExtendInfo(new ExtendInfo(couponPackageInfo));
                }
                return success;
            } else {
                return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_FAIL.getCode(), responseBaseVo.getMsg());
            }
        } catch (Exception e) {
            log.error("[券平台权益] 发放失败 request:{}", command, e);
            return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_UNKOWN.getCode(), e.getMessage());
        }
    }

    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        String ticketPakcageId = promotionEquityDO.getPropsInfo().getCouponPackageInfo().getTicketPackageId();
        if (ticketPakcageId == null) {
            throw new IllegalArgumentException("缺少券包ID");
        }
        //
        TicketPackageDTO ticketPackageDTO = userTicketClientFacade.getTicketPackage(ticketPakcageId);
        if (ticketPackageDTO == null) {
            throw new IllegalArgumentException("券包不存在,id:" + ticketPakcageId);
        }
        if (!Arrays.asList(1).contains(ticketPackageDTO.getStatus())) {
            throw new IllegalArgumentException(String.format("优惠券包[%s]状态异常，比如为已发布或者已开始，当前为 %s ",
                    ticketPackageDTO.getName(),
                    ticketPackageDTO.getStatus()));
        }
        return ValidateEquityResultBO.success();
    }

    private SendTicketParam createDefaultSendTicketParam(EquitySendCommandDO command) {
        SendTicketParam sendTicketParam = new SendTicketParam();
        Assert.notNull(command.getPropsInfo(), "缺少权益属性，commandId:" + command.getCommandId());
        Assert.notNull(command.getPropsInfo().getCouponPackageInfo(), "缺少优惠券包信息，commandId:" + command.getCommandId());
        String ticketPackageId = command.getPropsInfo().getCouponPackageInfo().getTicketPackageId();
        Assert.notBlank(ticketPackageId, "缺少优惠券包id，commandId:" + command.getCommandId());
        sendTicketParam.setTicketPackageId(ticketPackageId);
        sendTicketParam.setUserId(command.getUid());
        sendTicketParam.setType(EQUITY_TICKET_TYPE);
        //不能超过64位
        sendTicketParam.setCode(command.getBizNo());
        sendTicketParam.setScene(command.getPropsInfo().getScene());
        sendTicketParam.setBizFrom(command.getBizFrom());
        sendTicketParam.setActivityId(command.getPropsInfo().getActivityId());
        sendTicketParam.setPrizeId(String.valueOf(command.getPromotionEquityDO().getEquityId()));
        return sendTicketParam;
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        if (extendInfo.getCouponPackageInfo() == null
                || StringUtils.isEmpty(extendInfo.getCouponPackageInfo().getUserId())
                || StringUtils.isEmpty(extendInfo.getCouponPackageInfo().getUserTicketIds())) {
            return sendResultDetail;
        }
        UserTicketPackageEquityDTO userTicketPackageEquityDTO = new UserTicketPackageEquityDTO();
        List<UserTicketEquityDTO> userTicketEquityDTOList =
                equityControllerConvert.getUserTicketInfoList(
                        extendInfo.getCouponPackageInfo().getUserTicketIds(),
                        extendInfo.getCouponPackageInfo().getUserId());
        userTicketPackageEquityDTO.setUserTicketEquityDTOList(userTicketEquityDTOList);
        sendResultDetail.setUserTicketPackageEquityDTO(userTicketPackageEquityDTO);
        return sendResultDetail;
    }

}
