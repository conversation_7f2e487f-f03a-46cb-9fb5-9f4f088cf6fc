package com.ddmc.promoequity.domain.ablity;

public interface AbilityConstant {
    String BIZ_FROM_PREFIX = "bizFrom";

    interface Props {
        //      String PROMOTION_TYPE = "promotion_type";

        //发放活动次数
        String ACTIVITY_ID = "promotion_activity_id";
        String ACTIVITY_PLAY_NUM = "promotion_activity_play_num";


        //发放优惠券
        String TICKET_ID = "equity_ticket_id";
        String TICKET_PACKAGE_ID = "equity_ticket_package_id";
        String TICKET_ACTIVITY_ID = "ticket_promotion_activity_id";
        String TICKET_PRIZE_ID = "ticket_promotion_prize_id";
        String TICKET_SCENE = "ticket_promotion_scene";


        String POINT = "point";
    }

    interface ExtendsInfo {
        String USER_TICKET_ID = "equity_user_ticket_id";
        String USER_TICKET_PACKAGE_ID = "equity_user_ticket_package_id";
        String USER_ID = "equity_user_id";
    }

}
