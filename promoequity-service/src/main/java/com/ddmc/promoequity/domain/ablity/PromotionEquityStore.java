package com.ddmc.promoequity.domain.ablity;

import com.ddmc.promoequity.domain.ablity.entity.*;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.service.EquitySendCommandStateServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权益商店
 * 负责发送权益
 */
@Component
@Slf4j
public class PromotionEquityStore {

    @Resource
    private List<AbstractEquityAbility> abstractEquityAbilities;

    @Resource
    private EquitySendCommandStateServiceImpl equitySendCommandStateService;

    /**
     * 发送权益
     *
     * @param promotionEquityDO
     * @param equitySendDO
     * @return
     */
    public SendEquityResultBO sendEquity(PromotionEquityDO promotionEquityDO, EquitySendDO equitySendDO) {
        EquityAbility target = findSuitable(promotionEquityDO);
        return target.sendEquity(promotionEquityDO, equitySendDO);
    }

    /**
     * 验证权益是否可用
     *
     * @param promotionEquityDO
     */
    public void validate(PromotionEquityDO promotionEquityDO) {
        EquityAbility target = findSuitable(promotionEquityDO);
        target.validate(promotionEquityDO);
    }

    /**
     * 重试权益
     *
     * @param commandId
     * @return
     */
    public SendEquityResultBO retry(String commandId) {
        EquitySendCommandDO equitySendCommandDO = equitySendCommandStateService.detail(commandId);
        if (equitySendCommandDO == null) {
            throw new IllegalArgumentException("发送记录不存在");
        }
        EquityAbility target = findSuitable(equitySendCommandDO.getPromotionEquityDO());
        return target.retry(equitySendCommandDO);
    }

    /**
     * 查询权益配置项
     *
     * @param queryLabel
     * @return
     */
    public List<SelectLabel> queryTag(QueryLabel queryLabel) {
        EquityAbility target = findSuitable(queryLabel.getEquityTypeEnums());
        return target.label(queryLabel);
    }

    /**
     * 将equitySendDO中的发送属性填入原始的propInfo中返回
     *
     * @param promotionEquityDO
     * @param equitySendDO
     * @return
     */

    /**
     * 匹配权益职能类
     *
     * @param equity
     * @return
     */
    private EquityAbility findSuitable(PromotionEquityDO equity) {
        AbstractEquityAbility abstractEquityAbility =
                abstractEquityAbilities.stream().filter(item -> item.suit(equity.getEquityType())).findFirst().orElse(null);
        if (abstractEquityAbility == null) {
            throw new IllegalArgumentException(String.format("equity_type:%s exist no suitable equity",
                    equity.getEquityType()));
        }
        return abstractEquityAbility;
    }

    /**
     * 匹配权益职能类
     *
     * @param equityTypeEnums
     * @return
     */
    public EquityAbility findSuitable(EquityTypeEnums equityTypeEnums) {
        AbstractEquityAbility abstractEquityAbility =
                abstractEquityAbilities.stream().filter(item -> item.suit(equityTypeEnums)).findFirst().orElse(null);
        if (abstractEquityAbility == null) {
            throw new IllegalArgumentException(String.format("equity_type:%s exist no suitable equity",
                    equityTypeEnums));
        }
        return abstractEquityAbility;
    }


}
