package com.ddmc.promoequity.domain.entity;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.enums.BizFromEnums;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.enums.ExpireTypeEnums;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionEquityDO {
    private Long equityId;
    private EquityTypeEnums equityType;
    private String equityName;
    private BizFromEnums bizFrom;
    private String bizKey;
    private ExpireTypeEnums expireType;
    private Date expireTime;
    private String description;
    private String creator;
    private Date publishTime;
    private Date createTime;
    private Date updateTime;
    private BizStatus status;
    private PropsInfo propsInfo;
}
