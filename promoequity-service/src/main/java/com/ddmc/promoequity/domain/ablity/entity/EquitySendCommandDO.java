package com.ddmc.promoequity.domain.ablity.entity;

import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.Data;

@Data
public class EquitySendCommandDO {
    private Long id;
    /**
     * 发送id
     */
    private String commandId;

    /**
     * 业务类型
     */
    private String bizFrom;

    /**
     * 业务唯一键
     */
    private String bizNo;
    /**
     * 用户UID
     */
    private String uid;

    private String status;

    private ExtendInfo extendInfo;

    private PropsInfo propsInfo;
    /**
     * 权益
     */
    private PromotionEquityDO promotionEquityDO;
}
