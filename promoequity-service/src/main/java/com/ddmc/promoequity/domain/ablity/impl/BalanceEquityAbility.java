package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.common.exception.ServiceException;
import com.ddmc.promoequity.common.util.SignUtil;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.BalanceClientFacade;
import com.ddmc.promoequity.infra.facade.entity.SendBalanceParam;
import com.ddmc.promoequity.vo.BalanceInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.trade.balance.dto.resp.BalanceRechargeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.TreeMap;

import static com.netflix.config.DeploymentContext.ContextKey.appId;

/**
 * 余额权益
 *
 * <AUTHOR>
 * @Date 2021/9/24
 */
@Slf4j
@Component
public class BalanceEquityAbility extends AbstractEquityAbility {

    @Resource
    private BalanceClientFacade balanceClientFacade;

    @Value("${balance.send.api.toggle.enable:false}")
    private Boolean sendApiToggleEnable;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.BALANCE == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            if (sendApiToggleEnable) {
                SendBalanceParam param = buildSendBalanceParam(command);
                BalanceRechargeResp balanceRechargeResp = balanceClientFacade.sendBalance(param);
                log.info("doSendEquity::balanceRechargeResp={}", JSONUtil.toJsonStr(balanceRechargeResp));
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                if (balanceRechargeResp != null) {
                    BalanceInfo balanceInfo = command.getPropsInfo().getBalanceInfo();
                    balanceInfo.setCardPrice(StringUtils.isEmpty(balanceRechargeResp.getMoney()) ? BigDecimal.ZERO : new BigDecimal(balanceRechargeResp.getMoney()));
                    balanceInfo.setBizNo(command.getBizNo());
                    ExtendInfo extendInfo = new ExtendInfo(balanceInfo);
                    extendInfo.setBizCount(balanceInfo.getCardPrice());
                    success.setExtendInfo(extendInfo);
                }
                return success;
            }
            //调用
            String result = balanceClientFacade.sendBalance(command);
            if (StringUtils.isBlank(result)) {
                log.error("[发放余额]返回为空, command:{}", JSONUtil.toJsonStr(command));
                return SendEquityResultBO.fail("发送余额接口返回空");
            }
            JSONObject json = JSONUtil.parseObj(result);
            if (json != null && Boolean.TRUE.equals(json.getBool("success"))) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                JSONObject dataJson = json.getJSONObject("data");
                if (dataJson != null) {
                    BalanceInfo balanceInfo = command.getPropsInfo().getBalanceInfo();
                    balanceInfo.setCardPrice(dataJson.getBigDecimal("money"));
                    balanceInfo.setBizNo(command.getBizNo());
                    ExtendInfo extendInfo = new ExtendInfo(balanceInfo);
                    extendInfo.setBizCount(balanceInfo.getCardPrice());
                    success.setExtendInfo(extendInfo);
                }
                return success;
            } else {
                log.error("[发放余额]发放失败 req:{}, res:{}", JSONUtil.toJsonStr(command), result);
                return SendEquityResultBO.fail("发送余额失败，code：[" + json.get("code") + "],msg：[" + json.getStr("msg") +
                        "]");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Throwable e) {
            log.error("[发放余额] 余额发放异常", e);
            throw e;
        }
    }

    private SendBalanceParam buildSendBalanceParam(EquitySendCommandDO command) {
        SendBalanceParam param = SendBalanceParam.builder()
                .cardId(command.getPropsInfo().getBalanceInfo().getCardId())
                .serialNum(command.getBizNo())
                .uid(command.getUid())
                .build();
        return param;
    }

    /**
     * 创建权益校验
     *
     * @param promotionEquityDO
     * @return
     */
    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO.getPropsInfo(), "缺少余额券id");
        Assert.notNull(promotionEquityDO.getPropsInfo().getBalanceInfo(), "缺少余额券id");
        Assert.notNull(promotionEquityDO.getPropsInfo().getBalanceInfo().getCardId(), "缺少余额券id");
        return ValidateEquityResultBO.success();
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setBalanceInfo(extendInfo.getBalanceInfo());
        return sendResultDetail;
    }
}
