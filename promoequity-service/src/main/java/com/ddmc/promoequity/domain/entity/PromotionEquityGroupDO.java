package com.ddmc.promoequity.domain.entity;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.enums.BizLineEnums;
import com.ddmc.promoequity.enums.UseSceneEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromotionEquityGroupDO {
    private Long groupId;
    private String groupName;
    private String description;
    private BizLineEnums bizLine;
    private UseSceneEnums useScene;

    private String creator;
    private Date createTime;
    private Date updateTime;
    private Date publishTime;
    private BizStatus status;
    private List<PromotionEquityDO> equityList;
}
