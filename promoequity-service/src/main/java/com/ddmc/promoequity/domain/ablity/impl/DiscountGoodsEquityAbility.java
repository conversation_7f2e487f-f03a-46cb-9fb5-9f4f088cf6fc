package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import com.ddmc.activitycore.model.UserProductBenefitModel;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.UserActivityClientFacade;
import com.ddmc.promoequity.infra.facade.entity.DiscountGoodsParam;
import com.ddmc.promoequity.vo.DiscountGoodsInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 商品折扣品权益
 *
 * <AUTHOR>
 * @Date 2021/10/25
 */
@Component
@Slf4j
public class DiscountGoodsEquityAbility extends AbstractEquityAbility {

    @Resource
    private UserActivityClientFacade userActivityClientFacade;

    @Value("${goodsValidTime:30}")
    private Integer goodsValidTime;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.DISCOUNT_GOODS == equityTypeEnums;
    }


    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            DiscountGoodsParam discountGoodsParam = createDiscountGoodsParam(command);
            ResponseBaseVo<List<UserProductBenefitModel>> responseBaseVo =
                    userActivityClientFacade.createDiscountGoods(discountGoodsParam);
            if (responseBaseVo.isSuccess()) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                DiscountGoodsInfo discountGoodsInfo = command.getPropsInfo().getDiscountGoodsInfo();
                if(!CollectionUtils.isEmpty(responseBaseVo.getData())){
                    UserProductBenefitModel model = responseBaseVo.getData().get(0);
                    discountGoodsInfo.setUserGoodsId(model.getId());
                    discountGoodsInfo.setStartTime(discountGoodsParam.getStartTime());
                    discountGoodsInfo.setEndTime(discountGoodsParam.getEndTime());
                }
                ExtendInfo extendInfo = new ExtendInfo(discountGoodsInfo);
                success.setExtendInfo(extendInfo);
                return success;
            } else {
                return SendEquityResultBO.fail(responseBaseVo.getMsg());
            }
        } catch (Exception e) {
            log.error("[折扣商品权益] 发放失败 request:{}", command, e);
            return SendEquityResultBO.fail(e.getMessage());
        }
    }

    private DiscountGoodsParam createDiscountGoodsParam(EquitySendCommandDO command) {
        DiscountGoodsParam discountGoodsParam = new DiscountGoodsParam();
        DiscountGoodsInfo discountGoodsInfo = command.getPropsInfo().getDiscountGoodsInfo();
        discountGoodsParam.setGoodsId(discountGoodsInfo.getGoodsId());
        discountGoodsParam.setGoodsPrice(discountGoodsInfo.getPrice());
        // 商品折扣品有效期取发送这一刻30(默认)分钟内有效
        Long startTime = System.currentTimeMillis();
        Long endTime = startTime + goodsValidTime.intValue() * 60 * 1000;
        discountGoodsParam.setStartTime(startTime);
        discountGoodsParam.setEndTime(endTime);
        discountGoodsParam.setStationId(command.getPropsInfo().getStationId());
        discountGoodsParam.setUserId(command.getUid());
        discountGoodsParam.setActivityId(command.getPropsInfo().getActivityId());
        return discountGoodsParam;
    }

    /**
     * 发送参数校验
     *
     * @param equitySendDO
     * @return
     */
    @Override
    public ValidateEquityResultBO validate(EquitySendDO equitySendDO) {
        Assert.notNull(equitySendDO.getPropsInfo(), "缺少折扣品信息");
        Assert.isFalse(StringUtils.isBlank(equitySendDO.getStationId()) && StringUtils.isBlank(equitySendDO.getPropsInfo().getStationId()), "缺少站点id");
        Assert.notNull(equitySendDO.getPropsInfo().getDiscountGoodsInfo(), "缺少折扣品信息");
        Assert.notBlank(equitySendDO.getPropsInfo().getDiscountGoodsInfo().getGoodsId(), "缺少折扣品id");
        Assert.notNull(equitySendDO.getPropsInfo().getDiscountGoodsInfo().getPrice(), "缺少折扣品价格");
        Assert.notNull(equitySendDO.getPropsInfo().getDiscountGoodsInfo().getPrice(), "缺少折扣品价格");
        return ValidateEquityResultBO.success();
    }

    /**
     * 发送属性填充
     *
     * @param propsInfo
     * @param equitySendDO
     */
    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        DiscountGoodsInfo discountGoodsInfo =
                Optional.ofNullable(propsInfo.getDiscountGoodsInfo()).orElse(new DiscountGoodsInfo());
        discountGoodsInfo.setGoodsId(equitySendDO.getPropsInfo().getDiscountGoodsInfo().getGoodsId());
        discountGoodsInfo.setPrice(equitySendDO.getPropsInfo().getDiscountGoodsInfo().getPrice());
        propsInfo.setDiscountGoodsInfo(discountGoodsInfo);
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setDiscountGoodsInfo(extendInfo.getDiscountGoodsInfo());
        return sendResultDetail;
    }
}
