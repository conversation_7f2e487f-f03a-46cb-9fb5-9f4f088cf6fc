package com.ddmc.promoequity.domain.ablity.impl;

import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.vo.ExtendInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 实物权益
 *
 * <AUTHOR>
 * @Date 2021/9/23
 */
@Component
@Slf4j
public class StuffEquityAbility extends AbstractEquityAbility {
    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.STUFF == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        SendEquityResultBO success = SendEquityResultBO.success();
        success.setEquityId(command.getPromotionEquityDO().getEquityId());
        success.setEquityName(command.getPromotionEquityDO().getEquityName());
        success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
        success.setExtendInfo(new ExtendInfo(command.getPropsInfo().getStuffInfo()));
        return success;
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo){
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setStuffInfo(extendInfo.getStuffInfo());
        return sendResultDetail;
    }
}
