package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.common.exception.ServiceException;
import com.ddmc.promoequity.controller.convert.EquityControllerConvert;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.*;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.UserTicketClientFacade;
import com.ddmc.promoequity.infra.facade.entity.QueryTicketListParam;
import com.ddmc.promoequity.infra.facade.entity.SendTicketParam;
import com.ddmc.promoequity.vo.CouponInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.promoequity.vo.RiskInfo;
import com.ddmc.vouchercore.client.dto.TicketDTO;
import com.ddmc.vouchercore.client.request.sub.RiskCheckRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ddmc.promoequity.infra.facade.constants.TicketConstant.EQUITY_TICKET_TYPE;

/**
 * 优惠券权益，发券
 */
@Component
@Slf4j
public class CouponEquityAbility extends AbstractEquityAbility {

    @Resource
    private UserTicketClientFacade userTicketClientFacade;

    @Resource
    private EquityControllerConvert equityControllerConvert;


    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.COUPON == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            SendTicketParam sendTicketParam = createDefaultSendTicketParam(command);
            ResponseBaseVo responseBaseVo = userTicketClientFacade.sendTicket(sendTicketParam);
            if (responseBaseVo.isSuccess()) {
                SendEquityResultBO success = SendEquityResultBO.success();
                success.setEquityId(command.getPromotionEquityDO().getEquityId());
                success.setEquityName(command.getPromotionEquityDO().getEquityName());
                success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                if (responseBaseVo.getData() != null) {
                    CouponInfo couponInfo = new CouponInfo();
                    couponInfo.setUserTicketId(String.valueOf(responseBaseVo.getData()));
                    couponInfo.setUserId(command.getUid());
                    success.setExtendInfo(new ExtendInfo(couponInfo));
                }
                return success;
            } else if (responseBaseVo.getCode() == 11000000) {
                throw new ServiceException(ResultCodeEnum.SENDTICKET_UNKOWN.getCode(), responseBaseVo.getMsg() + "11000000");
            } else {
                return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_FAIL.getCode(), responseBaseVo.getMsg());
            }
        } catch (Exception e) {
            log.error("[券平台权益] 发放失败 request:{}", command, e);
            return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_UNKOWN.getCode(), e.getMessage());
        }
    }

    /**
     * 创建权益校验
     *
     * @param promotionEquityDO
     * @return
     */
    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO.getPropsInfo(), "缺少优惠券ID");
        Assert.notNull(promotionEquityDO.getPropsInfo().getCouponInfo(), "缺少优惠券ID");
        String ticketId = promotionEquityDO.getPropsInfo().getCouponInfo().getTicketId();
        Assert.notBlank(ticketId, "缺少优惠券ID");
        TicketDTO ticketDTO = userTicketClientFacade.getTicket(ticketId);
        Assert.notNull(ticketDTO, "优惠券不存在,id:" + ticketId);
        if (!Arrays.asList(1, 2).contains(ticketDTO.getStatus())) {
            throw new IllegalArgumentException(String.format("优惠券[%s]状态异常，比如为已发布或者已开始，当前为 %s ", ticketDTO.getName(),
                    ticketDTO.getStatus()));
        }
        return ValidateEquityResultBO.success();
    }

    @Override
    public List<SelectLabel> label(QueryLabel queryLabel) {
        //查询可用的优惠券列表
        QueryTicketListParam queryTicketListParam = new QueryTicketListParam();
        queryTicketListParam.setKeywords(queryLabel.getKeywords());
        List<TicketDTO> ticketDTOS = userTicketClientFacade.selectUsefullTicket(queryTicketListParam);
        if (CollectionUtils.isEmpty(ticketDTOS)) {
            return Lists.newArrayList();
        }
        return ticketDTOS.stream().map(ticket -> {
            SelectLabel selectLabel = new SelectLabel();
            selectLabel.setLabel(ticket.getName());
            selectLabel.setValue(ticket.getId());
            return selectLabel;
        }).collect(Collectors.toList());
    }

    private SendTicketParam createDefaultSendTicketParam(EquitySendCommandDO command) {
        SendTicketParam sendTicketParam = new SendTicketParam();
        Assert.notNull(command.getPropsInfo(), "缺少权益属性，commandId:" + command.getCommandId());
        Assert.notNull(command.getPropsInfo().getCouponInfo(), "缺少优惠券信息，commandId:" + command.getCommandId());
        String ticketId = command.getPropsInfo().getCouponInfo().getTicketId();
        Assert.notBlank(ticketId, "缺少优惠券ID，commandId:" + command.getCommandId());
        sendTicketParam.setTicketId(ticketId);
        sendTicketParam.setStartTime(command.getPropsInfo().getCouponInfo().getStartTime());
        sendTicketParam.setExpireTime(command.getPropsInfo().getCouponInfo().getExpireTime());
        sendTicketParam.setUserId(command.getUid());
        sendTicketParam.setType(EQUITY_TICKET_TYPE);
        //不能超过64位
        sendTicketParam.setCode(command.getBizNo());
        sendTicketParam.setBizFrom(command.getBizFrom());

        sendTicketParam.setActivityId(command.getPropsInfo().getActivityId());
        sendTicketParam.setPrizeId(String.valueOf(command.getPromotionEquityDO().getEquityId()));
        sendTicketParam.setScene(command.getPropsInfo().getScene());
        sendTicketParam.setIsRead(command.getPropsInfo().getCouponInfo().getIsRead());
        //风控信息
        RiskInfo riskInfo = command.getPropsInfo().getRiskInfo();
        if (Objects.nonNull(riskInfo)) {
            sendTicketParam.setRiskCheckRequest(RiskCheckRequest.builder()
                    .scene_code(riskInfo.getScene_code())
                    .user_id(riskInfo.getUser_id())
                    .user_mobile(riskInfo.getUser_mobile())
                    .device_id(riskInfo.getDevice_id())
                    .device_token(riskInfo.getDevice_token())
                    .device_model(riskInfo.getDevice_model())
                    .device_name(riskInfo.getDevice_name())
                    .api_version(riskInfo.getApi_version())
                    .app_client_id(riskInfo.getApp_client_id())
                    .ip(riskInfo.getIp())
                    .longitude(riskInfo.getLongitude())
                    .latitude(riskInfo.getLatitude())
                    .city_number(riskInfo.getCity_number())
                    .station_id(riskInfo.getStation_id())
                    .user_agent(riskInfo.getUser_agent())
                    .referer(riskInfo.getReferer())
                    .url(riskInfo.getUrl())
                    .os_version(riskInfo.getOs_version())
                    .idfa(riskInfo.getIdfa())
                    .idfv(riskInfo.getIdfv())
                    .wifi_name(riskInfo.getWifi_name())
                    .wifi_mac(riskInfo.getWifi_mac())
                    .imei(riskInfo.getImei())
                    .imsi(riskInfo.getImsi())
                    .oaid(riskInfo.getOaid())
                    .build());
        }
        return sendTicketParam;
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        if (extendInfo.getCouponInfo() == null
                || StringUtils.isEmpty(extendInfo.getCouponInfo().getUserId())
                || StringUtils.isEmpty(extendInfo.getCouponInfo().getUserTicketId())) {
            return sendResultDetail;
        }
        sendResultDetail.setUserTicketInfo(equityControllerConvert.getCouponSendResult(extendInfo.getCouponInfo().getUserId(),
                extendInfo.getCouponInfo().getUserTicketId()));
        return sendResultDetail;
    }

    /**
     * 发送属性填充
     *
     * @param propsInfo
     * @param equitySendDO
     */
    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        CouponInfo couponInfo =
                Optional.ofNullable(propsInfo.getCouponInfo()).orElse(new CouponInfo());
        if (equitySendDO.getPropsInfo() != null && equitySendDO.getPropsInfo().getCouponInfo() != null) {
            couponInfo.setStartTime(equitySendDO.getPropsInfo().getCouponInfo().getStartTime());
            couponInfo.setExpireTime(equitySendDO.getPropsInfo().getCouponInfo().getExpireTime());
            couponInfo.setIsRead(equitySendDO.getPropsInfo().getCouponInfo().getIsRead());
        }
        propsInfo.setCouponInfo(couponInfo);
    }

}
