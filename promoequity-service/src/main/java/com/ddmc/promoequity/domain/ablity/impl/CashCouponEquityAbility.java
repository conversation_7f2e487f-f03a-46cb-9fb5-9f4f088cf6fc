package com.ddmc.promoequity.domain.ablity.impl;

import com.csoss.monitor.api.cat.Cat;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.common.enums.ResultCodeEnum;
import com.ddmc.promoequity.common.exception.ServiceException;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.CashCouponFacade;
import com.ddmc.promoequity.infra.facade.entity.SendCashCouponParam;
import com.ddmc.promoequity.vo.CashCouponInfo;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.voucherprod.dto.wechatcash.response.SendCashTicketResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 代金券权益
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Slf4j
@Component
public class CashCouponEquityAbility extends AbstractEquityAbility {

    @Resource
    private CashCouponFacade cashCouponFacade;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.CASH_COUPON == equityTypeEnums;
    }


    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            SendCashCouponParam param = createCashCouponCommand(command);
            final ResponseBaseVo<SendCashTicketResponse> responseBaseVo = cashCouponFacade.sendCashCoupon(param);
            if (responseBaseVo.isSuccess()) {
                final SendCashTicketResponse sendCashCouponResp = responseBaseVo.getData();
                if (sendCashCouponResp != null && sendCashCouponResp.getSendResult()) {
                    SendEquityResultBO success = SendEquityResultBO.success();
                    success.setEquityId(command.getPromotionEquityDO().getEquityId());
                    success.setEquityName(command.getPromotionEquityDO().getEquityName());
                    success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
                    CashCouponInfo cashCouponInfo = command.getPropsInfo().getCashCouponInfo();
                    cashCouponInfo.setUserTicketId(sendCashCouponResp.getUserTicketId());
                    cashCouponInfo.setUserId(command.getUid());
                    success.setExtendInfo(new ExtendInfo(cashCouponInfo));
                    return success;
                } else {
                    Cat.logEvent("sendCashCoupon", "FAIL");
                    return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_FAIL.getCode(), responseBaseVo.getMsg());
                }
            } else if (responseBaseVo.getCode() == 11000000) {
                throw new ServiceException(ResultCodeEnum.SENDTICKET_UNKOWN.getCode(), responseBaseVo.getMsg() + "11000000");
            } else {
                Cat.logEvent("sendCashCoupon", "FAIL");
                return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_FAIL.getCode(), responseBaseVo.getMsg());
            }
        } catch (Throwable e) {
            Cat.logEvent("sendCashCoupon", "UNKOWN");
            log.error("[发放代金券] 代金券发放异常 request:{}", command, e);
            return SendEquityResultBO.fail(ResultCodeEnum.SENDTICKET_UNKOWN.getCode(), e.getMessage());
        }
    }

    private SendCashCouponParam createCashCouponCommand(EquitySendCommandDO command) {
        final CashCouponInfo cashCouponInfo = command.getPropsInfo().getCashCouponInfo();
        final SendCashCouponParam build = SendCashCouponParam.builder()
                .openId(cashCouponInfo.getOpenId())
                .requestNo(command.getBizNo())
                .userId(command.getUid())
                .ticketId(cashCouponInfo.getCashCouponId()).build();
        return build;
    }


    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setCashCouponInfo(extendInfo.getCashCouponInfo());
        return sendResultDetail;
    }

    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        final CashCouponInfo cashCouponInfo = Optional.ofNullable(propsInfo.getCashCouponInfo()).orElse(new CashCouponInfo());
        cashCouponInfo.setOpenId(equitySendDO.getPropsInfo().getCashCouponInfo().getOpenId());
        cashCouponInfo.setUserId(equitySendDO.getPropsInfo().getCashCouponInfo().getUserId());
        propsInfo.setCashCouponInfo(cashCouponInfo);
    }
}
