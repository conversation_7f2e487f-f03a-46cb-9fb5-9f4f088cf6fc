package com.ddmc.promoequity.domain.repository;

import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;

import java.io.Serializable;

/**
 * repository基础类
 *
 * @param <T>
 */
public interface BaseRepository<T> {
    /**
     * 按id查找
     */
    default T findById(Serializable id) {
        return null;
    }

    /**
     * 分页查找
     */
    default PageResult<T> findList(T param, PageInfo pageInfo) {
        return null;
    }

    /**
     * 保存或更新
     */
    default void insertOrUpdate(T t) {
    }

    /**
     * 更新状态
     */
    default long updateStatus(Serializable id, BizStatus from, BizStatus to) {
        return 0;
    }
}
