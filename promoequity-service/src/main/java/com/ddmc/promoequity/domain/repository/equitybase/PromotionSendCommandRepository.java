package com.ddmc.promoequity.domain.repository.equitybase;

import com.ddmc.promoequity.domain.entity.MorePageResult;
import com.ddmc.promoequity.domain.entity.vo.EquityCommandListQuery;
import com.ddmc.promoequity.domain.repository.BaseRepository;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;

public interface PromotionSendCommandRepository extends BaseRepository<TbPromotionEquitySendCommand> {
    MorePageResult<TbPromotionEquitySendCommand> findListByPreKey(EquityCommandListQuery query);
}
