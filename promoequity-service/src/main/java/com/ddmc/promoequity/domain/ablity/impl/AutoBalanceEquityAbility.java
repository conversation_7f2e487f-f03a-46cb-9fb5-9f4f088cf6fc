package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.common.exception.ServiceException;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.dto.SendResultDetail;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.BalanceClientFacade;
import com.ddmc.promoequity.infra.facade.entity.SendAutoBalanceParam;
import com.ddmc.promoequity.vo.*;
import com.ddmc.trade.balance.dto.resp.AutoBalanceRechargeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 自定义余额
 *
 * <AUTHOR>
 * @Date 2022/10/25
 */
@Slf4j
@Component
public class AutoBalanceEquityAbility extends AbstractEquityAbility {

    @Resource
    private BalanceClientFacade balanceClientFacade;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.AUTO_BALANCE == equityTypeEnums;
    }

    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        try {
            SendAutoBalanceParam param = buildSendBalanceParam(command);
            AutoBalanceRechargeResp autoBalanceRechargeResp  = balanceClientFacade.sendAutoBalance(param);
            log.info("doSendEquity::autoBalanceRechargeResp:{}", JSONUtil.toJsonStr(autoBalanceRechargeResp));
            SendEquityResultBO success = SendEquityResultBO.success();
            success.setEquityId(command.getPromotionEquityDO().getEquityId());
            success.setEquityName(command.getPromotionEquityDO().getEquityName());
            success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
            if (autoBalanceRechargeResp != null) {
                BalanceInfo balanceInfo = command.getPropsInfo().getBalanceInfo();
                balanceInfo.setCardPrice(StringUtils.isEmpty(autoBalanceRechargeResp.getMoney()) ? BigDecimal.ZERO : new BigDecimal(autoBalanceRechargeResp.getMoney()));
                balanceInfo.setBizNo(command.getBizNo());
                ExtendInfo extendInfo = new ExtendInfo(balanceInfo);
                extendInfo.setBizCount(balanceInfo.getCardPrice());
                success.setExtendInfo(extendInfo);
            }
            return success;
        } catch (Throwable e) {
            log.error("[发放自定义余额]异常", e);
            throw e;
        }
    }

    private SendAutoBalanceParam buildSendBalanceParam(EquitySendCommandDO command) {
        SendAutoBalanceParam param = SendAutoBalanceParam.builder()
                .serialNum(command.getBizNo())
                .rechargeMoney(command.getPropsInfo().getBalanceInfo().getCardPrice())
                .uid(command.getUid())
                .bizScene(command.getPropsInfo().getBalanceInfo().getBizScene())
                .activityId(command.getPropsInfo().getActivityId())
                .build();
        SmsInfo smsInfo = command.getPropsInfo().getBalanceInfo().getSmsInfo();
        if (smsInfo != null) {
            SmsInfo sms = SmsInfo.builder().smsId(smsInfo.getSmsId())
                    .signId(smsInfo.getSignId())
                    .smsPara(smsInfo.getSmsPara()).build();
            param.setSmsInfo(sms);
        }
        return param;
    }

    /**
     * 创建权益校验
     *
     * @param promotionEquityDO
     * @return
     */
    @Override
    public ValidateEquityResultBO validate(PromotionEquityDO promotionEquityDO) {
        Assert.notNull(promotionEquityDO.getPropsInfo(), "缺少余额券");
        Assert.notNull(promotionEquityDO.getPropsInfo().getBalanceInfo(), "缺少余额券");
        Assert.notNull(promotionEquityDO.getPropsInfo().getBalanceInfo().getCardPrice(), "缺少余额价值");
        return ValidateEquityResultBO.success();
    }

    @Override
    public SendResultDetail buildSendResultDetail(ExtendInfo extendInfo) {
        SendResultDetail sendResultDetail = new SendResultDetail();
        sendResultDetail.setBalanceInfo(extendInfo.getBalanceInfo());
        return sendResultDetail;
    }

    /**
     * 发送属性填充
     *
     * @param propsInfo
     * @param equitySendDO
     */
    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        propsInfo.getBalanceInfo().setSmsInfo(equitySendDO.getPropsInfo().getBalanceInfo().getSmsInfo());
        propsInfo.getBalanceInfo().setBizScene(equitySendDO.getPropsInfo().getBalanceInfo().getBizScene());
        propsInfo.getBalanceInfo().setCardPrice(equitySendDO.getPropsInfo().getBalanceInfo().getCardPrice());
    }
}
