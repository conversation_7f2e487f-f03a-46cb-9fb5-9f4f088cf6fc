package com.ddmc.promoequity.domain.repository.equitybase;

import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.repository.BaseRepository;
import com.ddmc.promoequity.dto.EquityRecordBizCountStatisticDTO;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;

import java.util.List;

public interface PromotionEquityRepository extends BaseRepository<TbPromotionEquity> {
    PageResult<TbPromotionEquity> queryList(PromotionEquityQuery query, PageInfo pageInfo);

    List<TbPromotionEquity> selectByIds(List<Long> ids);

    List<TbPromotionEquitySendStatistics> sendStatistics(String sendBizFrom,String sendBizKeys);
    EquityRecordBizCountStatisticDTO bizCountRecordStatistics(EquityRecordBizCountStatisticDTO request);

}
