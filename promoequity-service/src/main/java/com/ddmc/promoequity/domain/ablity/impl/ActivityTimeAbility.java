package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.core.lang.Assert;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.domain.ablity.AbstractEquityAbility;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.ablity.entity.ValidateEquityResultBO;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.infra.facade.PromotionActivityFacade;
import com.ddmc.promoequity.infra.facade.entity.IncreaseActivityTimeParam;
import com.ddmc.promoequity.vo.ExtendInfo;
import com.ddmc.promoequity.vo.PromotionInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 活动平台次数加一
 */
@Component
@Slf4j
public class ActivityTimeAbility extends AbstractEquityAbility {

    @Resource
    private PromotionActivityFacade promotionActivityFacade;

    @Override
    public boolean suit(EquityTypeEnums equityTypeEnums) {
        return EquityTypeEnums.PROMOTION == equityTypeEnums;
    }


    @Override
    protected SendEquityResultBO doSendEquity(EquitySendCommandDO command) {
        // 活动id
        String activityId = command.getPropsInfo().getActivityId();
        // 发送次数
        Integer playNum = command.getPropsInfo().getPromotionInfo().getActivityPlayNum();
        String uid = command.getUid();
        IncreaseActivityTimeParam increaseActivityTimeParam = new IncreaseActivityTimeParam();
        increaseActivityTimeParam.setActivityId(activityId);
        increaseActivityTimeParam.setUserId(uid);
        increaseActivityTimeParam.setBizNo(command.getBizNo());
        increaseActivityTimeParam.setBizFrom(command.getBizFrom());
        if (playNum != null) {
            increaseActivityTimeParam.setCount(playNum);
        }
        ResponseBaseVo responseBaseVo = promotionActivityFacade.increaseActivityTime(increaseActivityTimeParam);

        if (responseBaseVo.isSuccess()) {
            SendEquityResultBO success = SendEquityResultBO.success();
            success.setEquityId(command.getPromotionEquityDO().getEquityId());
            success.setEquityName(command.getPromotionEquityDO().getEquityName());
            success.setEquityType(command.getPromotionEquityDO().getEquityType().name());
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setBizCount(new BigDecimal(playNum));
            success.setExtendInfo(extendInfo);
            return success;
        } else {
            return SendEquityResultBO.fail(responseBaseVo.getMsg());
        }
    }

    /**
     * 发送参数校验
     *
     * @param equitySendDO
     * @return
     */
    @Override
    public ValidateEquityResultBO validate(EquitySendDO equitySendDO) {
        Assert.notNull(equitySendDO.getPropsInfo(), "缺少活动id");
        Assert.notBlank(equitySendDO.getPropsInfo().getActivityId(), "缺少活动id");
        Assert.notNull(equitySendDO.getPropsInfo().getPromotionInfo(), "缺少奖励次数");

        Integer playNum =
                equitySendDO.getPropsInfo().getPromotionInfo().getActivityPlayNum();
        if (!StringUtils.isEmpty(playNum) && playNum.intValue() <= 0) {
            throw new IllegalArgumentException("promotion_activity_play_num 必须大于0");
        }
        return ValidateEquityResultBO.success();
    }

    /**
     * 发送属性填充
     *
     * @param propsInfo
     * @param equitySendDO
     */
    @Override
    public void fieldEquityProp(PropsInfo propsInfo, EquitySendDO equitySendDO) {
        PromotionInfo promotionInfo =
                Optional.ofNullable(propsInfo.getPromotionInfo()).orElse(new PromotionInfo());
        promotionInfo.setActivityPlayNum(equitySendDO.getPropsInfo().getPromotionInfo().getActivityPlayNum());
        propsInfo.setPromotionInfo(promotionInfo);
    }
}
