package com.ddmc.promoequity.domain.ablity.entity;

import com.ddmc.promoequity.vo.PropsInfo;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
public class EquitySendDO {

    private Long equityId;
    /**
     * 业务类型
     */
    private String bizFrom;

    /**
     * 业务唯一键
     */
    private String bizNo;

    /**
     * 业务关键字段（如：活动id）
     */
    private String bizKey;
    /**
     * 用户UID
     */
    private String uid;

    private Integer scene;

    /**
     * 权益次数
     */
    private Integer equityCnt;

    private String description;

    private String stationId;

    private PropsInfo propsInfo;


}
