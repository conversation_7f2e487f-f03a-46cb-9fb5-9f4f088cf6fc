package com.ddmc.promoequity.domain.repository.equitybase;

import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityGroupQuery;
import com.ddmc.promoequity.domain.repository.BaseRepository;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroup;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquityGroupItem;

import java.util.List;

public interface PromotionEquityGroupRepository extends BaseRepository<TbPromotionEquityGroup> {

    PageResult<TbPromotionEquityGroup> queryList(PromotionEquityGroupQuery query, PageInfo pageInfo);

    List<TbPromotionEquityGroupItem> queryGroupItem(Long groupId);

    /**
     * 保存权益组数据
     */
    void addEquityItem(Long groupId, List<TbPromotionEquity> equityList);

    /**
     * 清楚权益
     */
    void clearEquityItem(Long groupId);
}
