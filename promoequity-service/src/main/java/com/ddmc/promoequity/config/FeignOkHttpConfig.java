package com.ddmc.promoequity.config;

import feign.Feign;
import okhttp3.ConnectionPool;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * 使用okhttp替换spring-feign默认httpclient
 */
@Configuration
@ConditionalOnClass(Feign.class)
@AutoConfigureBefore(FeignAutoConfiguration.class)
public class FeignOkHttpConfig {

    @Value("${httpclient.connect-timeout-seconds: 1}")
    private int connectTimeoutSeconds;

    @Value("${httpclient.read-timeout-seconds: 1}")
    private int readTimeoutSeconds;

    @Value("${httpclient.write-timeout-seconds: 1}")
    private int writeTimeoutSeconds;

    @Value("${httpclient.connection-pool.max-idle: 300}")
    private int maxIdle;

    @Value("${httpclient.connection-pool.keep-alive-duration-seconds: 300}")
    private int keepAliveDurationSeconds;

    @Bean
    public okhttp3.OkHttpClient okHttpClient() {
        return new okhttp3.OkHttpClient.Builder()
            .readTimeout(readTimeoutSeconds, TimeUnit.SECONDS)
            .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
            .writeTimeout(writeTimeoutSeconds, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(maxIdle, keepAliveDurationSeconds, TimeUnit.SECONDS))
            .build();
    }

}
