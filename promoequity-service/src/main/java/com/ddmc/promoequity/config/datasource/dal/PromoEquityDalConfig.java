package com.ddmc.promoequity.config.datasource.dal;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ddmc.jdbc.pool.config.apollo.ConfigBuilder;
import com.ddmc.jdbc.pool.datasource.DdmcDataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.ObjectUtils;

import javax.sql.DataSource;
import java.util.Map;


@MapperScan(basePackages = {"com.ddmc.promoequity.infra.repository.equityuser.mapper",
"com.ddmc.promoequity.infra.repository.equitybase.mapper"},
        sqlSessionTemplateRef = "promoEquityDalSessionTemplate")
@Configuration
@ConditionalOnProperty(value = "dal.mysql.enable", havingValue = "true", matchIfMissing = true)
public class PromoEquityDalConfig {
    @Value("${dal.resource.name:promoequitypromo_equity_gz_group}")
    private String dalResourceName;

    @Value("${dal.pool.max.pool.size:30}")
    private Integer maxPoolSize;
    @Value("${dal.pool.min.pool.size:10}")
    private Integer minPoolSize;

    @Value("${dal.resource.bindMaster:true}")
    private Boolean bindMaster;
    @Bean
    public DataSource promoEquityDalDataSource() {
        DdmcDataSource ds = new DdmcDataSource();
        Map<String, String> config =  ConfigBuilder.newInstance()
                .resourceName(dalResourceName)
                .maxPoolSize(maxPoolSize) //非必填
                .minPoolSize(minPoolSize)  //非必填
                .bindMaster(bindMaster)
                .build();
        ds.setConfigServiceMeta(config);
        ds.init();
        return ds;
    }

    private final Interceptor[] interceptors;

    public PromoEquityDalConfig(
            ObjectProvider<Interceptor[]> interceptorsProvider) {
        this.interceptors = interceptorsProvider.getIfAvailable();
    }

    @Bean
    public SqlSessionFactory promoEquityDalSessionFactory() throws Exception {
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(promoEquityDalDataSource());
        factory.setVfs(SpringBootVFS.class);
        if (!ObjectUtils.isEmpty(this.interceptors)) {
            factory.setPlugins(this.interceptors);
        }
        return factory.getObject();
    }

    @Bean
    public SqlSessionTemplate promoEquityDalSessionTemplate() throws Exception {
        return new SqlSessionTemplate(promoEquityDalSessionFactory());
    }

    @Bean
    public DataSourceTransactionManager dalTransaction() {
        return new DataSourceTransactionManager(promoEquityDalDataSource());
    }
}
