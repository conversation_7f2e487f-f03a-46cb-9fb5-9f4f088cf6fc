package com.ddmc.promoequity.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@EnableApolloConfig
@Configuration
public class ApolloConfig {
    /**
     * 埋占方式切换总开关
     */
    @Value("${promoequity.monitor.transfer.switch:true}")
    public boolean monitorTransferSwitch;

    /**
     * EquitySendStatistics 统计开关
     */
    @Value("${promoequity.EquitySendStatistics.switch:false}")
    public boolean equitySendStatisticsSwitch;
}
