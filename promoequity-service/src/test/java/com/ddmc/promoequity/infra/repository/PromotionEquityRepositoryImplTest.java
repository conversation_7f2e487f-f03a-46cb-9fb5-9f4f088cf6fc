package com.ddmc.promoequity.infra.repository;

import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;
import org.junit.Before;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;

/**
 * PromotionEquityRepositoryImpl 测试类
 * 主要测试 Apollo 开关功能
 */
public class PromotionEquityRepositoryImplTest extends BaseTest {

    @Resource
    private PromotionEquityRepositoryImpl promotionEquityRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试开关关闭时的正常逻辑
     */
    @Test
    public void testSendStatistics_SwitchOff() {
        // 设置开关为关闭状态
        ReflectionTestUtils.setField(promotionEquityRepository, "equitySendStatisticsQuerySwitch", false);
        
        // 测试参数为空的情况
        promotionEquityRepository.sendStatistics(null, null);
        
        // 测试正常的参数（这里会执行原有逻辑，但由于没有真实数据，会返回空列表）
        promotionEquityRepository.sendStatistics("test-biz-from", "test-biz-key");
        
        System.out.println("测试开关关闭状态完成");
    }

    /**
     * 测试开关开启时直接返回空列表
     */
    @Test
    public void testSendStatistics_SwitchOn() {
        // 设置开关为开启状态
        ReflectionTestUtils.setField(promotionEquityRepository, "equitySendStatisticsQuerySwitch", true);
        
        // 测试开关开启时的逻辑
        promotionEquityRepository.sendStatistics("test-biz-from", "test-biz-key");
        
        System.out.println("测试开关开启状态完成 - 应该直接返回空列表，不执行后续逻辑");
    }

    /**
     * 测试参数为空的情况
     */
    @Test
    public void testSendStatistics_EmptyParams() {
        // 设置开关为关闭状态
        ReflectionTestUtils.setField(promotionEquityRepository, "equitySendStatisticsQuerySwitch", false);
        
        // 测试参数为空
        promotionEquityRepository.sendStatistics("", "");
        
        System.out.println("测试空参数完成");
    }
}
