package com.ddmc.promoequity.infra.service;

import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;
import org.junit.Before;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;

/**
 * EquitySendCommandStateServiceImpl 测试类
 * 主要测试 Apollo 开关功能
 */
public class EquitySendCommandStateServiceImplTest extends BaseTest {

    @Resource
    private EquitySendCommandStateServiceImpl equitySendCommandStateService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试开关关闭时的正常逻辑
     */
    @Test
    public void testIncreaseSendCount_SwitchOff() {
        // 设置开关为关闭状态
        ReflectionTestUtils.setField(equitySendCommandStateService, "equitySendStatisticsSwitch", false);

        // 测试 commandId 为 null 的情况
        equitySendCommandStateService.increaseSendCount(null);

        // 测试正常的 commandId（这里会执行原有逻辑，但由于没有真实数据，会在内部处理异常）
        equitySendCommandStateService.increaseSendCount("test-command-id");

        System.out.println("测试开关关闭状态完成");
    }

    /**
     * 测试开关开启时直接返回
     */
    @Test
    public void testIncreaseSendCount_SwitchOn() {
        // 设置开关为开启状态
        ReflectionTestUtils.setField(equitySendCommandStateService, "equitySendStatisticsSwitch", true);

        // 测试开关开启时的逻辑
        equitySendCommandStateService.increaseSendCount("test-command-id");

        System.out.println("测试开关开启状态完成 - 应该直接返回，不执行后续逻辑");
    }

    /**
     * 测试 commandId 为 null 的情况
     */
    @Test
    public void testIncreaseSendCount_NullCommandId() {
        // 设置开关为关闭状态
        ReflectionTestUtils.setField(equitySendCommandStateService, "equitySendStatisticsSwitch", false);

        // 测试 commandId 为 null
        equitySendCommandStateService.increaseSendCount(null);

        System.out.println("测试 null commandId 完成");
    }
}
