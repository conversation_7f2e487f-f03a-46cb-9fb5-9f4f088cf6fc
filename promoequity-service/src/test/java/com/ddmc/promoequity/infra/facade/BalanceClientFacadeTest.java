package com.ddmc.promoequity.infra.facade;

import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.domain.ablity.entity.BalanceResult;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.enums.BalanceBizSceneEnums;
import com.ddmc.promoequity.infra.facade.entity.SendAutoBalanceParam;
import com.ddmc.promoequity.infra.facade.entity.SendBalanceParam;
import com.ddmc.promoequity.test.BaseTest;
import com.ddmc.promoequity.vo.BalanceInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.promoequity.vo.SmsInfo;
import com.ddmc.trade.balance.dto.resp.AutoBalanceRechargeResp;
import com.ddmc.trade.balance.dto.resp.BalanceRechargeResp;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public class BalanceClientFacadeTest extends BaseTest {
    @Autowired
    private BalanceClientFacade balanceClientFacade;


    @Test
    public void testSendBalance() {
        String req = "{\"uid\":\"5d403bc5ebec583b1daad4ed\",\"appId\":\"promoequity-service\"" +
                ",\"recharge_source\":1,\"channel_id\":\"5db903414df3e395618b634b\"," +
                "\"serial_num\":\"5d403bc5ebec583b1daad4ed_522710025_40107\",\"card_id\":\"611a07a84fb79739238ce06e\"" +
                ",\"token\":\"ece03bebf2fb2c004c7f4309712b2a99\"}";
        SendBalanceParam param = JSONUtil.toBean(req, SendBalanceParam.class);

        param.setCardId("611a07a84fb79739238ce06e");
        param.setRechargeSource(1);
        param.setSerialNum("5d403bc5ebec583b1daad4ed_522710025_40107");
        param.setUid("5d403bc5ebec583b1daad4ed");
//        param.setChannelId("5db903414df3e395618b634b");
        final BalanceRechargeResp balanceRechargeResp = balanceClientFacade.sendBalance(param);
        System.out.println(JSONUtil.toJsonStr(balanceRechargeResp));
    }

    /**
     * 新接口
     */
    @Test
    public void testQueryBalanceList() {
        final List<BalanceResult> balanceResults = balanceClientFacade.queryBalanceList(null);
        System.out.println(JSONUtil.toJsonStr(balanceResults));
    }

    /**
     * 老接口-加开关
     */
    @Test
    public void testTestQueryBalanceList() {
        final List<BalanceResult> balanceResults = balanceClientFacade.queryBalanceList();
        System.out.println(JSONUtil.toJsonStr(balanceResults));
    }

    @Test
    public void testTestSendBalance() {
        PropsInfo propsInfo = new PropsInfo();
        BalanceInfo balanceInfo = new BalanceInfo();
        balanceInfo.setCardId("611a07a84fb79739238ce06e");
        propsInfo.setBalanceInfo(balanceInfo);
        EquitySendCommandDO commandDO = new EquitySendCommandDO();
        commandDO.setPropsInfo(propsInfo);
        commandDO.setBizNo("5d403bc5ebec583b1daad4ed_522710025_40107");

        final String s = balanceClientFacade.sendBalance(commandDO);
        System.out.println(JSONUtil.toJsonStr(s));
    }

    @Test
    public void testSendAutoBalance() {
        SendAutoBalanceParam param = new SendAutoBalanceParam();
        param.setBizScene(BalanceBizSceneEnums.SINGLE_FREE_ORDER.name());
        param.setSmsInfo(SmsInfo.builder().smsPara(Lists.newArrayList("免单测试", "1", "2", "3"))
                .smsId(485).signId(1).build());
        param.setSerialNum(UUID.randomUUID().toString());
        param.setUid("600827516e7eb80001a149bf");
        final AutoBalanceRechargeResp autoBalanceRechargeResp = balanceClientFacade.sendAutoBalance(param);
        System.out.println(JSONUtil.toJsonStr(autoBalanceRechargeResp));
    }
}