package com.ddmc.promoequity.controller;

import cn.hutool.json.JSONUtil;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.EquityDTO;
import com.ddmc.promoequity.dto.request.SaveOrUpdateEquityRequest;
import com.ddmc.promoequity.dto.request.UpdateEquityStateRequest;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

public class EquityAdminControllerTest extends BaseTest {

    @Resource
    private EquityAdminController equityAdminController;

    /**
     * 创建代金券权益
     */
    @Test
    public void testSaveOrUpdateV1() {
        String param = "{\n" +
                "  \"creator\": \"陈振飞\",\n" +
                "  \"equityType\": \"CASH_COUPON\",\n" +
                "  \"equityName\": \"代金券测试002\",\n" +
                "  \"description\": \"代金券: 测试002(满2.00减1.00)\",\n" +
                "  \"propsInfo\": {\n" +
                "    \"cashCouponInfo\": {\n" +
                "      \"cashCouponId\": \"123245666333\",\n" +
                "      \"cashCouponName\": \"微信代金券测试001(满2.00减1.00)\"\n" +
                "    },\n" +
                "    \"scene\": 0\n" +
                "  }\n" +
                "}\n" +
                "\n";
        SaveOrUpdateEquityRequest request = JSONUtil.toBean(param, SaveOrUpdateEquityRequest.class);
        final ResponseBaseVo<EquityDTO> equityDTOResponseBaseVo = equityAdminController.saveOrUpdateV1(request);
        final Long equityId = equityDTOResponseBaseVo.getData().getEquityId();
        System.out.println(equityId);
    }

    private Long saveOrUpdateV1() {
        String param = "{\n" +
                "  \"creator\": \"陈振飞\",\n" +
                "  \"equityType\": \"CASH_COUPON\",\n" +
                "  \"equityName\": \"代金券测试002\",\n" +
                "  \"description\": \"代金券: 测试002(满2.00减1.00)\",\n" +
                "  \"propsInfo\": {\n" +
                "    \"cashCouponInfo\": {\n" +
                "      \"cashCouponId\": \"123245666333\",\n" +
                "      \"cashCouponName\": \"微信代金券测试001(满2.00减1.00)\"\n" +
                "    },\n" +
                "    \"scene\": 0\n" +
                "  }\n" +
                "}\n" +
                "\n";
        SaveOrUpdateEquityRequest request = JSONUtil.toBean(param, SaveOrUpdateEquityRequest.class);
        final ResponseBaseVo<EquityDTO> equityDTOResponseBaseVo = equityAdminController.saveOrUpdateV1(request);
        final Long equityId = equityDTOResponseBaseVo.getData().getEquityId();
        System.out.println(equityId);
        return equityId;
    }


    /**
     * 创建并发布
     */
    @Test
    public void testSaveOrUpdateV1AndPublish() {
        final Long equityId = saveOrUpdateV1();
        UpdateEquityStateRequest request = new UpdateEquityStateRequest();
        request.setEquityId(equityId);
        final ResponseBaseVo publish = equityAdminController.publish(request);
        System.out.println(JSONUtil.toJsonStr(publish));
        Assert.assertTrue("发布权益失败", publish.isSuccess());
    }


}