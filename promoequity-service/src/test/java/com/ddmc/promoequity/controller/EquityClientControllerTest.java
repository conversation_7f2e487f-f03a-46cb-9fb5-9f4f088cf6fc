package com.ddmc.promoequity.controller;

import cn.hutool.json.JSONUtil;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.SendEquityResult;
import com.ddmc.promoequity.dto.request.SendEquityRequest;
import com.ddmc.promoequity.test.BaseTest;
import org.apache.kafka.common.network.Send;

import javax.annotation.Resource;

public class EquityClientControllerTest extends BaseTest {


    @Resource
    private EquityClientController equityClientController;


    @Resource
    public void testSendEquity() {
        String param = "{\n" +
                "  \"uid\": \"63ec870523e9c2001f09a094\",\n" +
                "  \"bizNo\": \"63ec870523e9c2001f09a0941124081676445827777\",\n" +
                "  \"bizFrom\": \"promomission\",\n" +
                "  \"bizKey\": \"AC201000000000005039937\",\n" +
                "  \"equityId\": 1435241722677424130,\n" +
                "  \"propsInfo\": {\n" +
                "    \"activityId\": \"AC201000000000005039937\",\n" +
                "    \"promotionInfo\": {\n" +
                "      \"activityPlayNum\": 5\n" +
                "    },\n" +
                "    \"scene\": 0\n" +
                "  }\n" +
                "}";
        SendEquityRequest request = JSONUtil.toBean(param, SendEquityRequest.class);
        final ResponseBaseVo<SendEquityResult> sendEquityResultResponseBaseVo = equityClientController.sendEquity(request);
        System.out.println(JSONUtil.toJsonStr(sendEquityResultResponseBaseVo));
    }
}