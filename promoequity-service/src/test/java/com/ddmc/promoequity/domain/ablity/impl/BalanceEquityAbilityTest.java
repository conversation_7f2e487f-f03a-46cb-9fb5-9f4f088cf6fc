package com.ddmc.promoequity.domain.ablity.impl;

import cn.hutool.json.JSONUtil;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendCommandDO;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class BalanceEquityAbilityTest extends BaseTest {


    @Resource
    private BalanceEquityAbility balanceEquityAbility;

    @Test
    public void testDoSendEquity() {
        String req = "{\"uid\":\"5d403bc5ebec583b1daad4ed\",\"appId\":\"promoequity-service\",\"recharge_source\":1,\"channel_id\":\"5db903414df3e395618b634b\",\"serial_num\":\"5d403bc5ebec583b1daad4ed_522710025_40107\",\"card_id\":\"611a07a84fb79739238ce06e\",\"token\":\"ece03bebf2fb2c004c7f4309712b2a99\"}";
        final EquitySendCommandDO equitySendCommandDO = JSONUtil.toBean(req, EquitySendCommandDO.class);
        final SendEquityResultBO sendEquityResultBO = balanceEquityAbility.doSendEquity(equitySendCommandDO);
        System.out.println(JSONUtil.toJsonStr(sendEquityResultBO));
    }
}