package com.ddmc.promoequity.test.domain;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquityGroupDO;
import com.ddmc.promoequity.enums.BizLineEnums;
import com.ddmc.promoequity.enums.UseSceneEnums;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityGroupQuery;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.service.EquityDomainService;
import com.ddmc.promoequity.domain.service.EquityGroupDomainService;
import com.ddmc.promoequity.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class EquityDomainGroupTest extends BaseTest {

    @Resource
    private EquityGroupDomainService equityGroupDomainService;

    @Resource
    private EquityDomainService equityDomainService;

    private PromotionEquityGroupDO buildGroupDO() {
        //查询出现有的可用的权益组
        PromotionEquityQuery query = new PromotionEquityQuery();
        query.setBizStatus(BizStatus.PUBLISH);
        PageResult<PromotionEquityDO> result = equityDomainService.queryList(query, new PageInfo());
        List<PromotionEquityDO> equityList = result.getResult();

        PromotionEquityGroupDO groupDO = new PromotionEquityGroupDO();
        groupDO.setGroupName("权益组测试-20210828-error-回滚");
        groupDO.setDescription("权益组测试描述");
        groupDO.setBizLine(BizLineEnums.PROMOTION);
        groupDO.setUseScene(UseSceneEnums.PROMOTION);
        groupDO.setEquityList(equityList);
        return groupDO;
    }

    @Test
    public void testCreate() {
        //查询出现有的可用的权益组
        PromotionEquityGroupDO groupDO = buildGroupDO();
        //保存
        equityGroupDomainService.createEquityGroup(groupDO);
    }

    @Test
    public void queryGroup() {
        Long groupId = 1430496786142949378L;
        PromotionEquityGroupDO groupDO =
                equityGroupDomainService.queryEquityGroup(groupId);
        System.out.println(JSON.toJSONString(groupDO));
    }

    @Test
    public void testUpdate() {
        Long groupId = 1430496786142949378L;
        PromotionEquityGroupDO groupDO =
                equityGroupDomainService.queryEquityGroup(groupId);

        PromotionEquityQuery query = new PromotionEquityQuery();
        query.setBizStatus(BizStatus.PUBLISH);
        PageResult<PromotionEquityDO> result = equityDomainService.queryList(query, new PageInfo(3, 10));
        groupDO.setEquityList(result.getResult());

        equityGroupDomainService.updateEquityGroup(groupDO);

    }

    @Test
    public void queryGroupByPage() {
        PromotionEquityGroupQuery query = new PromotionEquityGroupQuery();
        PageResult<PromotionEquityGroupDO> result = equityGroupDomainService.queryList(query, new PageInfo());
        Assert.notNull(result.getResult());
    }

    @Test
    public void testPublish() {
        Long groupId = 1430496786142949378L;
        equityGroupDomainService.publish(groupId);
    }

    @Test
    public void testAbandon() {
        Long groupId = 1430429703421960193L;
        equityGroupDomainService.abandon(groupId);
    }
}
