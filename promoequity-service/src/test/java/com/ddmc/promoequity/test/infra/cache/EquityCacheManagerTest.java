package com.ddmc.promoequity.test.infra.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.promoequity.App;
import com.ddmc.promoequity.infra.cache.EquityCacheManager;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * EquityCacheManager 单元测试
 * 测试权益缓存管理器的 getEquity 和 getEquities 方法
 */
@Slf4j
@SpringBootTest(classes = App.class)
@RunWith(SpringRunner.class)
public class EquityCacheManagerTest {

    @Resource
    private EquityCacheManager equityCacheManager;

    @Resource
    private PromotionEquityMapper promotionEquityMapper;

    // 测试用的权益 ID，确保数据库中存在
    private Long existEquityId;
    private final Long nonExistEquityId = 999999L;
    private List<TbPromotionEquity> equities;

    @Before
    public void setUp() {
        // 从数据库中获取一个存在的权益 ID 用于测试
        LambdaQueryWrapper<TbPromotionEquity> wrapper = Wrappers.<TbPromotionEquity>lambdaQuery()
                .orderByDesc(TbPromotionEquity::getCreateTime)
                .last("limit 1000");
        equities = promotionEquityMapper.selectList(wrapper);
        Assert.assertTrue("数据库中至少应该存在两个权益", CollectionUtils.size(equities) >= 2);
        existEquityId = equities.get(0).getEquityId();
        log.info("测试准备完成，existEquityId={}, nonExistEquityId={}", existEquityId, nonExistEquityId);
    }

    // ================================
    // getEquity 方法测试
    // ================================

    @Test
    public void testGetEquity_ValidId() {
        TbPromotionEquity result = equityCacheManager.getEquity(existEquityId);

        Assert.assertNotNull("获取有效权益 ID 应该返回非空结果", result);
        Assert.assertEquals("权益 ID 应该匹配", existEquityId, result.getEquityId());
    }

    @Test
    public void testGetEquity_NonExistId() {
        TbPromotionEquity result = equityCacheManager.getEquity(nonExistEquityId);

        Assert.assertNull("获取不存在的权益 ID 应该返回 null", result);
    }

    @Test
    public void testGetEquity_NullId() {
        TbPromotionEquity result = equityCacheManager.getEquity(null);

        Assert.assertNull("传入 null ID 应该返回 null", result);
    }

    @Test
    public void testGetEquity_ZeroId() {
        TbPromotionEquity result = equityCacheManager.getEquity(0L);

        Assert.assertNull("传入 0 ID 应该返回 null", result);
    }

    @Test
    public void testGetEquity_NegativeId() {
        TbPromotionEquity result = equityCacheManager.getEquity(-1L);

        Assert.assertNull("传入负数 ID 应该返回 null", result);
    }

    @Test
    public void testGetEquity_CacheHit() {
        // 第一次访问，从数据库加载
        TbPromotionEquity firstResult = equityCacheManager.getEquity(existEquityId);

        // 第二次访问，应该从缓存获取
        TbPromotionEquity secondResult = equityCacheManager.getEquity(existEquityId);

        Assert.assertNotNull("第一次获取应该成功", firstResult);
        Assert.assertNotNull("第二次获取应该成功", secondResult);
        Assert.assertEquals("两次获取的结果应该一致", firstResult.getEquityId(), secondResult.getEquityId());
    }

    // ================================
    // getEquities 方法测试
    // ================================

    @Test
    public void testGetEquities_ValidIds() {
        List<Long> validIds = Lists.newArrayList(existEquityId);
        List<TbPromotionEquity> result = equityCacheManager.getEquities(validIds);

        Assert.assertNotNull("获取有效权益 ID 列表应该返回非空结果", result);
        Assert.assertFalse("结果列表不应该为空", result.isEmpty());
        Assert.assertEquals("结果数量应该匹配", 1, result.size());
        Assert.assertEquals("权益 ID 应该匹配", existEquityId, result.get(0).getEquityId());
    }

    @Test
    public void testGetEquities_MixedIds() {
        List<Long> mixedIds = Lists.newArrayList(existEquityId, nonExistEquityId);
        List<TbPromotionEquity> result = equityCacheManager.getEquities(mixedIds);

        // 因为包含不存在的 ID，方法会返回 null（根据当前实现逻辑）
        Assert.assertNull("包含不存在的权益 ID 应该返回 null", result);
    }

    @Test
    public void testGetEquities_AllExistIds() {
        List<Long> existEquityIds = Arrays.asList(
                equities.get(0).getEquityId(),
                equities.get(1).getEquityId()
        );

        List<TbPromotionEquity> result = equityCacheManager.getEquities(existEquityIds);

        Assert.assertNotNull("获取多个存在的权益 ID 应该返回非空结果", result);
        Assert.assertEquals("结果数量应该匹配", 2, result.size());
    }

    @Test
    public void testGetEquities_EmptyList() {
        List<TbPromotionEquity> result = equityCacheManager.getEquities(Collections.emptyList());

        Assert.assertNull("传入空列表应该返回 null", result);
    }

    @Test
    public void testGetEquities_NullList() {
        List<TbPromotionEquity> result = equityCacheManager.getEquities(null);

        Assert.assertNull("传入 null 列表应该返回 null", result);
    }

    @Test
    public void testGetEquities_InvalidIds() {
        List<Long> invalidIds = Arrays.asList(null, 0L, -1L);
        List<TbPromotionEquity> result = equityCacheManager.getEquities(invalidIds);

        Assert.assertNull("传入无效 ID 列表应该返回 null", result);
    }

    @Test
    public void testGetEquities_DuplicateIds() {
        List<Long> duplicateIds = Arrays.asList(existEquityId, existEquityId, existEquityId);
        List<TbPromotionEquity> result = equityCacheManager.getEquities(duplicateIds);

        Assert.assertNotNull("获取重复 ID 列表应该返回非空结果", result);
        Assert.assertEquals("去重后应该只返回一个结果", 1, result.size());
        Assert.assertEquals("权益 ID 应该匹配", existEquityId, result.get(0).getEquityId());
    }

    @Test
    public void testGetEquities_CacheAndDatabase() {
        // 先获取一个权益放入缓存
        TbPromotionEquity cachedEquity = equityCacheManager.getEquity(existEquityId);
        Assert.assertNotNull("预先缓存权益应该成功", cachedEquity);

        // 获取多个权益 ID，其中包含已缓存的
        Long anotherEquityId = null;
        for (TbPromotionEquity equity : equities) {
            if (!equity.getEquityId().equals(existEquityId)) {
                anotherEquityId = equity.getEquityId();
                break;
            }
        }

        Assert.assertNotNull("找不到另一个不同的权益 ID", anotherEquityId);

        List<Long> mixedIds = Arrays.asList(existEquityId, anotherEquityId);
        List<TbPromotionEquity> result = equityCacheManager.getEquities(mixedIds);

        Assert.assertNotNull("缓存和数据库混合查询应该成功", result);
        Assert.assertEquals("结果数量应该匹配", 2, result.size());
    }

    // ================================
    // 性能测试
    // ================================

    @Test
    public void testPerformance_GetEquity() {
        int iterations = 100;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < iterations; i++) {
            TbPromotionEquity result = equityCacheManager.getEquity(existEquityId);
            Assert.assertNotNull("每次获取都应该成功", result);
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;

        log.info("性能测试完成: {} 次调用耗时 {} ms，平均每次 {} ms", iterations, totalTime, avgTime);
        Assert.assertTrue("平均响应时间应该小于 10ms", avgTime < 10.0);
    }

    @Test
    public void testPerformance_GetEquities() {
        // 取前几个权益 ID 进行测试
        List<Long> testIds = equities.stream()
                .limit(Math.min(5, equities.size()))
                .map(TbPromotionEquity::getEquityId)
                .collect(java.util.stream.Collectors.toList());

        int iterations = 50;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < iterations; i++) {
            List<TbPromotionEquity> result = equityCacheManager.getEquities(testIds);
            Assert.assertNotNull("每次批量获取都应该成功", result);
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;

        log.info("批量查询性能测试完成: {} 次调用耗时 {} ms，平均每次 {} ms", iterations, totalTime, avgTime);
        Assert.assertTrue("平均响应时间应该小于 50ms", avgTime < 50.0);
    }
} 