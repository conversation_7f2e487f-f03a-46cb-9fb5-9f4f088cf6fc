package com.ddmc.promoequity.test.controller;

import com.alibaba.fastjson.JSON;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.controller.EquityAdminController;
import com.ddmc.promoequity.controller.EquityClientController;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.dto.EquityDTO;
import com.ddmc.promoequity.dto.MorePageResultDTO;
import com.ddmc.promoequity.dto.PageDTO;
import com.ddmc.promoequity.dto.SendEquityResult;
import com.ddmc.promoequity.dto.request.QueryEquityListRequest;
import com.ddmc.promoequity.dto.request.SendEquityRequest;
import com.ddmc.promoequity.dto.request.SendRecordRequest;
import com.ddmc.promoequity.test.BaseTest;
import com.ddmc.promoequity.vo.BalanceInfo;
import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.utils.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class ControllerTest extends BaseTest {

    @Resource
    private EquityAdminController equityAdminController;

    @Resource
    private EquityClientController equityClientController;

    @Test
    public void testPage() {
        QueryEquityListRequest request = new QueryEquityListRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        System.out.println(JSON.toJSONString(request));
        ResponseBaseVo<PageDTO<EquityDTO>> result = equityAdminController.queryList(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testQueryById() {
        Long equityId = 1434378269368541185L;
        ResponseBaseVo<EquityDTO> result = equityAdminController.detail(equityId);
        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void testSaveOrUpdate() {
        EquityDTO equityDTO = new EquityDTO();
        /*equityDTO.setEquityType(EquityTypeEnums.COUPON.name());
        equityDTO.setEquityName("满98减20优惠券-测试-01");
        //    equityDTO.setBizFrom(BizFromEnums.COUPON.name());
        // equityDTO.setBizKey("60b1aeda2fee3abf738b7719");
        //    equityDTO.setExpireType(ExpireTypeEnums.LIMIT.name());
        //    equityDTO.setExpireTime(new Date().getTime());
        equityDTO.setDescription("优惠券权益描述");
        equityDTO.setCreator("qzm");
        //equityDTO.setStatus(BizStatus.NEW.name());
        Map<String, String> maps = new HashMap<>();
        maps.put(AbilityConstant.Props.TICKET_ID, "60b1aeda2fee3abf738b7719");*/
        //equityDTO.setProps(maps);

        equityDTO.setEquityType(EquityTypeEnums.AUTO_BALANCE.name());
        equityDTO.setEquityName("自定义余额");
        PropsInfo propsInfo = new PropsInfo();
        BalanceInfo balanceInfo  = new BalanceInfo();
        balanceInfo.setCardPrice(new BigDecimal(1.50));
        balanceInfo.setCardName("余额1块5毛");
        balanceInfo.setBizNo(UUID.randomUUID().toString());
        propsInfo.setBalanceInfo(balanceInfo);
        equityDTO.setPropsInfo(propsInfo);
        System.out.println(JsonUtil.toJSON(equityDTO));
        ResponseBaseVo<EquityDTO> result = equityAdminController.saveOrUpdate(equityDTO);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSend(){
        Long equityId = 1435241722677424130L;
        String uid = "60c710aa5b4d510001add236";
        SendEquityRequest request = new SendEquityRequest();
        request.setBizFrom("promomission");
        request.setBizNo("promomission8211631266955271");
        request.setUid(uid);
        request.setEquityId(equityId);
        Map<String,String> props = new HashMap<>();
        props.put("promotion_activity_id","AC201000000000000015567");
        props.put("promotion_activity_play_num","4");
//        request.setProps(props);

        System.out.println(JSON.toJSONString(request));
        ResponseBaseVo<SendEquityResult> result =equityClientController.sendEquity(request);
        System.out.println(JsonUtil.toJSON(result));
    }

    public static void main(String[] args) {
        System.out.println("4".matches("\\d+"));
    }

    @Test
    public void testQuery(){
        SendRecordRequest request = new SendRecordRequest();
        request.setBizFrom("promomission");
        request.setUid("5bcaf2d26aa8c15ba5ec4a99");
        ResponseBaseVo<MorePageResultDTO<SendEquityResult>> res = equityClientController.sendRecord(request);
        System.out.println(JSON.toJSONString(res));
    }
}
