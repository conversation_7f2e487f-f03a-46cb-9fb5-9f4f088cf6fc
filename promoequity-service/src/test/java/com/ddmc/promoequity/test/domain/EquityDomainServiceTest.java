package com.ddmc.promoequity.test.domain;

import cn.hutool.core.lang.Assert;
import com.ddmc.promoequity.common.enums.BizStatus;
import com.ddmc.promoequity.domain.ablity.AbilityConstant;
import com.ddmc.promoequity.domain.ablity.entity.EquitySendDO;
import com.ddmc.promoequity.domain.ablity.entity.SelectLabel;
import com.ddmc.promoequity.domain.ablity.entity.SendEquityResultBO;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.PromotionEquityDO;
import com.ddmc.promoequity.domain.entity.PromotionEquitySendStatisticsDO;
import com.ddmc.promoequity.enums.BizFromEnums;
import com.ddmc.promoequity.enums.EquityTypeEnums;
import com.ddmc.promoequity.enums.ExpireTypeEnums;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.service.EquityDomainService;
import com.ddmc.promoequity.test.BaseTest;
import com.ddmc.utils.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CyclicBarrier;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;


@Slf4j
public class EquityDomainServiceTest extends BaseTest {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private EquityDomainService equityDomainService;

    //成功创建
    @Test
    public void testCreate() {
        PromotionEquityDO promotionEquityDO = new PromotionEquityDO();
        promotionEquityDO.setEquityType(EquityTypeEnums.COUPON);
        promotionEquityDO.setEquityName("20210830-测试_1");
        promotionEquityDO.setBizFrom(BizFromEnums.COUPON);
        promotionEquityDO.setBizKey("60b1aeda2fee3abf738b7719");
        promotionEquityDO.setExpireType(ExpireTypeEnums.LIMIT);
        promotionEquityDO.setExpireTime(new Date());
        promotionEquityDO.setDescription("优惠券权益描述");
        promotionEquityDO.setCreator("qzm");
        promotionEquityDO.setStatus(BizStatus.NEW);
        Map<String, String> maps = new HashMap<>();
        maps.put(AbilityConstant.Props.TICKET_ID, "60b1aeda2fee3abf738b7719");
//        promotionEquityDO.setProps(maps);
        System.out.println(String.format("创建前：%s", JsonUtil.toJSON(promotionEquityDO)));
        equityDomainService.createEquity(promotionEquityDO);
        System.out.println("创建后id：" + promotionEquityDO.getEquityId());
        PromotionEquityDO dbData = equityDomainService.queryById(promotionEquityDO.getEquityId());
        System.out.println(String.format("创建后：%s", JsonUtil.toJSON(dbData)));
        //发布
        equityDomainService.publishEquity(promotionEquityDO.getEquityId());
    }

    @Test
    public void testQueryByPage() {
        PageResult<PromotionEquityDO> result = equityDomainService.queryList(new PromotionEquityQuery(), new PageInfo());
        System.out.println(JsonUtil.toJSON(result));
    }

    //修改权益
    @Test
    public void testUpdate() {
        PromotionEquityDO promotionEquityDO = new PromotionEquityDO();
        promotionEquityDO.setEquityId(1430131043788173314L);
        promotionEquityDO.setEquityName("满100减20修改");
        equityDomainService.updateEquity(promotionEquityDO);
    }

    @Test
    public void testPublish() {
        equityDomainService.publishEquity(1434378269368541185L);
    }

    @Test
    public void testAbandon() {
        equityDomainService.abandonEquity(1434378269368541185L);
    }


    private PromotionEquityDO creatNewEquity() {
        PromotionEquityDO promotionEquityDO = new PromotionEquityDO();
        promotionEquityDO.setEquityType(EquityTypeEnums.PROMOTION);
        promotionEquityDO.setEquityName("翻牌活动次数加一");
        promotionEquityDO.setBizFrom(BizFromEnums.PROMOTION);
        promotionEquityDO.setExpireType(ExpireTypeEnums.UN_LIMIT);
        promotionEquityDO.setDescription("优惠券权益描述");
        promotionEquityDO.setCreator("qzm");
        promotionEquityDO.setStatus(BizStatus.NEW);
        Map<String, String> maps = new HashMap<>();
     //   maps.put("couponId", "sdsadss122dsd");
//        promotionEquityDO.setProps(maps);
        return promotionEquityDO;
    }


    @Test
    public void testWholeQueue() {
        //创建
        PromotionEquityDO newEquity = creatNewEquity();
        equityDomainService.createEquity(newEquity);
        //发布
        equityDomainService.publishEquity(newEquity.getEquityId());
        //再次查询
        PromotionEquityDO publishPersis = equityDomainService.queryById(newEquity.getEquityId());

        System.out.println(JsonUtil.toJSON(publishPersis));

    }

    @Test
    public void testPageQuery() {
        PageInfo pageInfo = new PageInfo();
        PageResult<PromotionEquityDO> result
                = equityDomainService.queryList(new PromotionEquityQuery(), pageInfo);
        log.info("testPageQuery,pageIndex:{},pageSize:{},totalPage:{},count:{}", result.getIndex(), result.getSize(),
                result.getTotal(), result.getCount());
        for (int i = 1; i <= result.getTotal(); i++) {
            PageResult<PromotionEquityDO> temp
                    = equityDomainService.queryList(new PromotionEquityQuery(), new PageInfo(i, 10));
            Assert.isTrue(temp.getCount() == result.getCount());
            Assert.isTrue(temp.getTotal() == result.getTotal());
            Assert.notNull(temp.getResult());
        }
    }

    /**
     * 测试发送权益
     */
    @Test
    public void testSendEquity() {
        String uid = "5d424117a5d5a6d4469a5502";
        EquitySendDO equitySendDO = EquitySendDO.builder()
                .equityId(1434818982193192962L)
                .bizFrom("local-activity19")
                .bizNo("20210907-211")
                .uid(uid)
//                .props(new HashMap<>())
                .build();
        SendEquityResultBO result = equityDomainService.sendEquity(equitySendDO);
        System.out.println(JsonUtil.toJSON(result));
    }


    @Test
    public void testSendEquity111() {
        String uid = "5d424117a5d5a6d4469a5502";
        Map<String, String> props = new HashMap<>();
        props.put(AbilityConstant.Props.ACTIVITY_ID, "活动id");
        EquitySendDO equitySendDO = EquitySendDO.builder()
                .equityId(1434378269368541185L)
                .bizFrom("local-activity19")
                .bizNo("20210907-1")
                .uid(uid)
//                .props(props)
                .build();
        SendEquityResultBO result = equityDomainService.sendEquity(equitySendDO);
        System.out.println(JsonUtil.toJSON(result));
    }


    @Test
    public void testBatchSend() {
        CyclicBarrier cyclicBarrier = new CyclicBarrier(5);
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        AtomicInteger atomicInteger = new AtomicInteger();
        for (int i = 0; i < 5; i++) {
            executorService.submit(() -> {
                String uid = "5d424117a5d5a6d4469a5502";

                EquitySendDO equitySendDO = EquitySendDO.builder()
                        .equityId(1434378269368541185L)
                        .bizFrom("local-activity-auto16")
                        .bizNo("20210906_" + atomicInteger.addAndGet(1))
                        .uid(uid)
//                        .props(new HashMap<>())
                        .build();
                try {
                    cyclicBarrier.await();
                    System.out.println("开始执行");
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (BrokenBarrierException e) {
                    e.printStackTrace();
                }
                equityDomainService.sendEquity(equitySendDO);
                System.out.println("发送完毕");
            });
        }
        try {
            Thread.sleep(30 * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void equityStatistics() {
        String bizFrom = "promocore";
        List<PromotionEquitySendStatisticsDO> sendStatisticsDOS = equityDomainService.equityStatistics(bizFrom,"AC201000000000004706472",null);
        System.out.println(JsonUtil.toJSON(sendStatisticsDOS));
    }


    @Test
    public void testRetry() {
        String commandId = "1630047921859";
        equityDomainService.retrySendEquity(commandId);
    }


    @Test
    public void testQueryTag() {
        List<SelectLabel> selectLabels = equityDomainService.queryTagList(EquityTypeEnums.COUPON, "测试");
        System.out.println(JsonUtil.toJSON(selectLabels));
    }


}
