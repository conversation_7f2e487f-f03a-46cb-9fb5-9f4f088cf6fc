package com.ddmc.promoequity.test;

import com.ddmc.promoequity.App;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;


@SpringBootTest(classes = App.class)
@RunWith(SpringRunner.class)
public class BaseTest {

    @BeforeClass
    public static void init() {
        System.setProperty("env", "FAT");
        System.setProperty("apollo.cluster", "TE");
        System.setProperty("spring.cloud.zookeeper.connect-string", "zk1pub.te.test.srv.mc.dd:2181,zk2pub.te.test.srv.mc.dd:2181,zk3pub.te.test.srv.mc.dd:2181");
        System.setProperty("spring.cloud.zookeeper.discovery.register", "false");
        System.setProperty("log.home", System.getProperty("user.dir") + File.separator + "logs");
    }
}
