package com.ddmc.promoequity.test.mapper;

import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class DalMapperTest extends BaseTest {
    @Resource
    private PromotionEquitySendCommandMapper promotionEquitySendCommandMapper;

    @Test
    public void testSendCommandDal() {
        System.out.println(promotionEquitySendCommandMapper.selectByCommandId("1122"));

    }
}
