package com.ddmc.promoequity.test.repository;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ddmc.promoequity.domain.entity.PageInfo;
import com.ddmc.promoequity.domain.entity.PageResult;
import com.ddmc.promoequity.domain.entity.vo.PromotionEquityQuery;
import com.ddmc.promoequity.domain.repository.equitybase.PromotionEquityRepository;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class RepositoryTest extends BaseTest {
    @Resource
    private PromotionEquityRepository promotionEquityRepository;

    @Test
    public void findById(){
        TbPromotionEquity result = promotionEquityRepository.findById(2);
        System.out.println(JSONUtil.toJsonStr(result));
    }

    @Test
    public void findByList(){
        PromotionEquityQuery query = new PromotionEquityQuery();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setIndex(1);
        pageInfo.setSize(2);
        PageResult<TbPromotionEquity> result = promotionEquityRepository.queryList(query,pageInfo);
        System.out.println(JSON.toJSONString(result));
    }
}
