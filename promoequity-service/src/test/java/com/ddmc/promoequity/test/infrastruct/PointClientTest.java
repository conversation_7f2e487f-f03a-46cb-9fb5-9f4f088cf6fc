package com.ddmc.promoequity.test.infrastruct;

import com.alibaba.fastjson.JSON;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.point.client.enums.FromTypeEnum;
import com.ddmc.promoequity.test.BaseTest;
import com.ddmc.userpoint.api.NewPointClient;
import com.ddmc.userpoint.api.OpPointClient;
import com.ddmc.userpoint.api.request.DecreaseRequestReq;
import com.ddmc.userpoint.api.request.IncreaseRequestReq;
import com.ddmc.userpoint.api.request.PointExchangeReq;
import com.ddmc.userpoint.api.response.PointExchangeVo;
import com.ddmc.userpoint.api.response.PointTotalVo;
import com.ddmc.userpoint.api.response.ResultVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2021/1/6
 */
public class PointClientTest extends BaseTest {

    @Autowired
    private OpPointClient opPointClient;
    @Autowired
    private NewPointClient newPointClient;

    @Test
    public void sendUserPoint() {
        String userId = "6264f5cc7c764b001e655612";
        Integer pointNum = 10;
        String description = "测试php转java";
        String orderNumber = UUID.randomUUID().toString();
        Integer type = 16;
        ResultVo<Object> objectResultVo = opPointClient.awardPoint(userId, type, pointNum, orderNumber, description);
        System.out.println(JSON.toJSON(objectResultVo));
    }


    @Test
    public void testGetUserTotalPoint() {
        String userId = "6264f5cc7c764b001e655612";
        ResponseBaseVo<Integer> userTotalPoint = newPointClient.getUserTotalPoint(userId);
        System.out.println(JSON.toJSON(userTotalPoint));
    }

    @Test
    public void testDecrease() {
        DecreaseRequestReq request = new DecreaseRequestReq();
        request.setRequestId(UUID.randomUUID().toString());
        request.setDescription("go转java扣减");
        request.setPoint(1);
        request.setUserId("6264f5cc7c764b001e655612");
        request.setRequestTime(System.currentTimeMillis());
        request.setFromType(FromTypeEnum.USER_SIGN_DRAW_LOTTERY.getCode());
        request.setOrderNumber("");
        ResponseBaseVo<Boolean> decrease = newPointClient.decrease(request);
        System.out.println(JSON.toJSON(decrease));
    }

    @Test
    public void testIncrease() {
        IncreaseRequestReq request = new IncreaseRequestReq();
        request.setRequestId(UUID.randomUUID().toString());
        request.setDescription("go转java增加");
        request.setPoint(2);
        request.setUserId("6264f5cc7c764b001e655612");
        request.setRequestTime(System.currentTimeMillis());
        request.setFromType(FromTypeEnum.USER_SIGN_DRAW_LOTTERY_PRIZE.getCode());
        request.setOrderNumber("");
        ResponseBaseVo<Boolean> decrease = newPointClient.increase(request);
        System.out.println(JSON.toJSON(decrease));
    }


    @Test
    public void testUserPoint() {
        String userId = "6264f5cc7c764b001e655612";
        ResultVo<PointTotalVo> pointTotalVoResultVo = opPointClient.userPoint(userId);
        System.out.println(JSON.toJSON(pointTotalVoResultVo));
    }

    @Test
    public void testPointExchange() {
        String userId = "6264f5cc7c764b001e655612";
        ResultVo<PointExchangeVo> pointExchangeVoResultVo = opPointClient.pointExchange(userId, 25, 4, "php转java兑换", null);
        System.out.println(JSON.toJSON(pointExchangeVoResultVo));
    }
}
