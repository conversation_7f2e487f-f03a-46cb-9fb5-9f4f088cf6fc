package com.ddmc.promoequity.test.infrastruct;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import com.ddmc.promoequity.infra.service.IDGenerator;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class TestIDGenerator extends BaseTest {
    @Resource
    private IDGenerator idGenerator;
    @Test
    public void nextVal(){
        System.out.println(idGenerator.commandId("xxx"));
    }

    @Resource
    private PromotionEquitySendCommandMapper promotionEquitySendCommandMapper;

    @Test
    public void testTimestamp(){
       List<TbPromotionEquitySendCommand> datas =  promotionEquitySendCommandMapper.selectList(new QueryWrapper<>());
        System.out.println(JSON.toJSONString(datas));
    }
}
