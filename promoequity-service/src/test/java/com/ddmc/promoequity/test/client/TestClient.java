package com.ddmc.promoequity.test.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.gateway.bg.client.model.AuthInfo;
import com.ddmc.promoequity.common.util.HttpUtil;
import com.ddmc.promoequity.infra.facade.UserTicketClientFacade;
import com.ddmc.promoequity.infra.facade.entity.SendTicketParam;
import com.ddmc.promoequity.test.BaseTest;
import com.ddmc.vouchercore.admin.TicketAdminApi;
import com.ddmc.vouchercore.admin.utils.VoucherCoreSignUtils;
import com.ddmc.vouchercore.admin.vo.ListSimpleTicketVo;
import com.ddmc.vouchercore.client.dto.TicketDTO;
import com.ddmc.vouchercore.client.dto.sub.PageInfoDTO;
import com.ddmc.vouchercore.client.response.PageQueryResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.Socket;
import java.nio.CharBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class TestClient extends BaseTest {

    @Resource
    private TicketAdminApi ticketAdminApi;

    @Resource
    private UserTicketClientFacade userTicketClientFacade;


    @Test
    public void test() {
        ListSimpleTicketVo.Req request = new ListSimpleTicketVo.Req();
        request.setStatusList(Arrays.asList(1, 2));
        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        pageInfoDTO.setPageIndex(1);
        pageInfoDTO.setPageSize(10);
        request.setPageInfo(pageInfoDTO);
        String appKey = "promoequity-service";
        request.setAppName(appKey);
        request.setIdNumberOrName("测试");
        VoucherCoreSignUtils.generateRequestParam(request, "iweui28yu8219ui9i9i");
//        AuthInfo authInfo = AuthUtil.getAuthInfo();
        AuthInfo defaultAuthInfo = new AuthInfo();
        defaultAuthInfo.setUsername("AuthComponent");
        defaultAuthInfo.setEmail("<EMAIL>");
        // 圣杰 te 的 token
        defaultAuthInfo.setToken("60a387b6fe854aabfe7d85b7");
        ResponseBaseVo<PageQueryResult<ListSimpleTicketVo.Resp>> res =
                ticketAdminApi.pageQueryBySimple(JSON.toJSONString(defaultAuthInfo), request);
        System.out.println(JSON.toJSONString(res));
    }

    @Test
    public void testSendTicket() {
        SendTicketParam param = new SendTicketParam();
        param.setBizFrom("PROMORECALL");
        param.setUserId("5f86683ff8b6e500010df1f9");
        param.setTicketId("6225b8ce092fdd000132e9f5");
        param.setType("equity");
        param.setCode("5f86683ff8b6e500010df1f9_-1141136162_126923");
        param.setActivityId("AC201000000000004818811");
        param.setPrizeId("1501134311798681602");
        param.setScene(23);
        ResponseBaseVo vo = userTicketClientFacade.sendTicket(param);
        System.out.println(JSON.toJSONString(vo));
    }

    @Test
    public void testTicket() throws JsonProcessingException {
        TicketDTO ticketDTO = userTicketClientFacade.getTicket("6139acab5058b200014687c8");
        System.out.println(JSON.toJSONString(ticketDTO));
    }

    @Test
    public void testGeo() {
        Map<String, Object> param = new HashMap<>();
        param.put("seq", 212123);
        param.put("action", 4);
        param.put("limit", 100);
        param.put("type", 4);
        Map<String, Object> pointer = new HashMap<>();
        pointer.put("longitude", "121.628968");
        pointer.put("latitude", "31.205653");
        param.put("pointer", pointer);

        String res = HttpUtil.doPostByAppJSON("http://geo1srv.te.test.srv.mc.dd:1600", param);
        System.out.println(res);
    }

    @Test
    public void testTcp() throws IOException {
        String host = "geo1srv.te.test.srv.mc.dd";
        int port = 1600;
        Map<String, Object> param = new HashMap<>();
        param.put("seq", (int) (Math.random() * Integer.MAX_VALUE));
        param.put("action", 4);
        param.put("limit", 100);
        param.put("type", 4);
        Map<String, Object> pointer = new HashMap<>();
        pointer.put("longitude", 121.628968);
        pointer.put("latitude", 31.205653);
        param.put("point", Arrays.asList(121.628968, 31.205653));

        Socket socket = new Socket(host, port);
        System.out.println("参数：" + JSON.toJSONString(param));
        socket.getOutputStream().write(JSON.toJSONString(param).getBytes(StandardCharsets.UTF_8));
        socket.getOutputStream().flush();
        System.out.println("发送成功，接受返回");

        InputStream in = socket.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(in));
        CharBuffer buf = CharBuffer.allocate(800);
        StringBuilder to = new StringBuilder();
        while (reader.read(buf) != -1) {
            buf.flip();
            to.append(buf);
            buf.clear();

            try {
                JSONObject jsonObject = JSONObject.parseObject(to.toString());
                break;
            } catch (Exception e) {

            }
        }

        System.out.println(to.toString());
    }
}
