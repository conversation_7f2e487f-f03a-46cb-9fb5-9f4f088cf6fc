package com.ddmc.promoequity.test.mapper;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquity;
import com.ddmc.promoequity.infra.repository.equitybase.entity.TbPromotionEquitySendStatistics;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquityMapper;
import com.ddmc.promoequity.infra.repository.equitybase.mapper.PromotionEquitySendStatisticsMapper;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendCommand;
import com.ddmc.promoequity.infra.repository.equityuser.entity.TbPromotionEquitySendRecord;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendCommandMapper;
import com.ddmc.promoequity.infra.repository.equityuser.mapper.PromotionEquitySendRecordMapper;
import com.ddmc.promoequity.test.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class MapperTest extends BaseTest {

    @Resource
    private PromotionEquityMapper promotionEquityMapper;

    @Resource
    private PromotionEquitySendCommandMapper commandMapper;

    @Resource
    private PromotionEquitySendRecordMapper recordMapper;

    @Resource
    private PromotionEquitySendStatisticsMapper promotionEquitySendStatisticsMapper;

    @Test
    public void getCommand() {
        List<TbPromotionEquitySendCommand> commands = commandMapper.queryUserCommand("5d424117a5d5a6d4469a5502");
        System.out.println(JSON.toJSONString(commands));
    }

    @Test
    public void test(){
        TbPromotionEquitySendStatistics result =
                promotionEquitySendStatisticsMapper.selectById(111);
        System.out.println(result);
    }

    @Test
    public void getRecord() {
        List<TbPromotionEquitySendRecord> recordList = recordMapper.findByCommandId("2051878");
        System.out.println(JSON.toJSONString(recordList));
    }

    @Test
    public void testMapper() {
        List<TbPromotionEquity> data = promotionEquityMapper.selectList(new QueryWrapper<>());
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    public void testQueryPage() {
        Page<TbPromotionEquity> pageList =
                promotionEquityMapper.selectPage(new Page<>(1, 2), null);
        System.out.println(JSON.toJSONString(pageList));
    }
}
