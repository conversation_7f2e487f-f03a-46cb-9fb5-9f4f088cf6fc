version = '1.0.6-SNAPSHOT'
test.enabled = true

dependencies {
    // spring cloud
    compile('org.springframework.boot:spring-boot-starter-web')
    compile("org.springframework.cloud:spring-cloud-context")
    compile('org.springframework.boot:spring-boot-starter-actuator')
    compile('org.springframework.cloud:spring-cloud-starter-openfeign')

    compile('org.springframework.cloud:spring-cloud-starter-zookeeper-discovery') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
    }
    compile('org.apache.zookeeper:zookeeper:3.4.9') {
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
    // 配置中心
    compile("com.ctrip.framework.apollo:apollo-client:1.7.0")
    compile "com.ctrip.framework.apollo:apollo-core:1.7.0"
    // mybatis and mysql
    compile('org.springframework.boot:spring-boot-starter-jdbc')
    compile('org.springframework:spring-tx:5.1.5.RELEASE')
//    compile('org.mybatis:mybatis-spring:1.3.2')
//    compile('org.mybatis:mybatis:3.4.6')

//    compile('org.apache.shardingsphere:sharding-jdbc-spring-boot-starter:4.1.1')

    compile 'com.baomidou:mybatis-plus-boot-starter:*******'
    // DAL
//    compile('com.ddmc:ddmc-jdbc-driver:1.0.1-RELEASE')
//    compile('com.ddmc:ddmc-jdbc-pool:1.1.2-RELEASE')
    compile('com.ddmc:ddmc-jdbc-pool')
    // redis
    //implementation('org.springframework.data:spring-data-redis')
    implementation('io.lettuce:lettuce-core:5.1.3.RELEASE')
    // swagger
    compile 'io.springfox:springfox-swagger2:2.7.0'
    compile 'io.springfox:springfox-swagger-ui:2.7.0'
    // log
    compile('log4j:log4j:1.2.17') {force = true}
    compile('org.springframework.kafka:spring-kafka:2.2.3.RELEASE')
    compile('com.github.danielwegener:logback-kafka-appender:0.2.0-RC2')
    compile('com.ddmc:ddmc-log')
    compile('org.slf4j:slf4j-api:1.7.25')
    compile('org.slf4j:log4j-over-slf4j:1.7.25')
    // cat
    // soa
    compile('com.ddmc.soa:spring-cloud-ddmc')
    // util
    compile('com.ddmc:core:1.1.1-SNAPSHOT')
    // 三方库
    compile('org.apache.httpcomponents:httpclient:4.5.12')
    compile('com.alibaba:fastjson:1.2.83')
    compile('cn.hutool:hutool-dfa:5.2.1')
    compile('commons-validator:commons-validator:1.6')
    compile('commons-codec:commons-codec:1.10')
    compile('io.github.openfeign.form:feign-form:3.8.0')
    compile('io.github.openfeign.form:feign-form-spring:3.8.0')
    compile('io.github.openfeign:feign-gson:9.5.1')
    compile('io.github.openfeign:feign-okhttp')
    // coding 简化
    compile('org.projectlombok:lombok:1.18.12')
    // 测试相关
    testCompile('org.springframework.boot:spring-boot-starter-test')
    testCompile('junit:junit:4.13')


    //活动平台
    compile('com.ddmc:promocore-client:1.1.4-SNAPSHOT')

    //券基础服务
    compile('com.ddmc:couponbase-client:1.0.5-RELEASE')

//    compile('com.ddmc:promoequity-client:1.0.4-SNAPSHOT') {
//        exclude group: 'com.ddmc', module: 'vouchercore-client'
//    }

    //网关
    compile('com.ddmc:gateway-client:1.7.4-SNAPSHOT')

    //B端网关
    compile('com.ddmc:gateway-starter:1.7.5-SNAPSHOT')
    // 老积分 5底停用
    compile("com.ddmc:point-client:1.1.0-SNAPSHOT")
    // 新积分
    compile("com.ddmc:op-userpoint-api:1.0.2-RELEASE")
    // 商品折扣
    compile('com.ddmc:promo-bi-client:1.0.0-SNAPSHOT') {
        exclude group: 'org.mongodb'
    }
    compile('com.ddmc:vouchercore-client:1.4.1-RELEASE')
    compile project(':promoequity-client')

    // CSOSS-REDIS
    compile 'csoss-redis:spring-data-redis-core'
    compile('com.ddmc:trade-balance-client:1.7.0-RELEASE')

    compile("com.ddmc:preferbase-client:1.1.9-RELEASE")

    compile("com.ddmc:voucherprod-client:1.1.1-RELEASE")

    compile('com.csoss:monitor-agent')

    compile('com.ddmc:vip-client:1.4.6-RELEASE')

    implementation('com.github.ben-manes.caffeine:caffeine:2.8.4')

    compile('org.apache.logging.log4j:log4j-api:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-core:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-to-slf4j:2.20.0') { force = true }
}
