version = '1.0.0-SNAPSHOT'
test.enabled = true
jar.enabled = true
bootJar.enabled = false
repositories {
    mavenCentral()
}

dependencies {
    // 测试相关
    compile 'org.springframework.boot:spring-boot-starter-test'
    compile group: 'junit', name: 'junit', version: '4.13'

    compile project(":promoequity-client")
    compile project(":promoequity-service")

    compile('org.apache.logging.log4j:log4j-api:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-core:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-to-slf4j:2.20.0') { force = true }
}
