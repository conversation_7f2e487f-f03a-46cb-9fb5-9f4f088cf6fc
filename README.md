
## promoequity 项目介绍

1、服务项目层次结构介绍

- {领域}-client: 领域服务客户端，上游工程可以引用该工程，调用里面提供的服务客户端接口，获取 "{领域}-service" 的处理结果
- {领域}-service: 领域服务工程，部署后提供对应领域服务
- {领域}-test: 领域服务测试，提供测试服务

2、{领域}-service目录介绍

- common 放置公共信息，如constant，enums,util,exception 等
- config: 配置信息，通常放置 @Configuration注解的类. 各种配置信息Apollo, feign, Swagger, redis ,mq
- controller: controller放置的地方
- domain: domain层 包含entity
- infra:  基础设施层 包含dao 
- service: service层放置的地方
- App 启动类

3、接口测试方法

```txt
  1、安装Postman
  2、打开Postman，选择POST方法，填写接口URL
  3、点击请求区域的Body tab，选择"raw"、 content-type 选择 "JSON(application/json)"
  4、如果有入参如下填写，eg：
            {
                "appKey":"IOS1213123214",
                "sign":"sign",
                "timestamp": 1231241423423,
                "param":{"id":"59967f1fdd9087a03a8e5444"}
            }
  5、点击Send 按钮
```


4、Swagger 接口测试

- swagger地址: http://localhost:port/swagger-ui.html

5、模型定义规范

```txt
模型: do, entity, dto, vo, 其中do与数据库字段一一对应

参数: request, response, param, result => 包装模型

1. 数据库模型使用 DO
2. 内部模型使用 Entity
3. 对端上透出的模型使用 VO
4. 对内RPC模型使用 DTO
5. 模型转换
   DTO  VO
    |   |
   entity
     |
     DO
```

6.接口命名规范

```txt
1.查询接口定义规则
- 单个查询 getXxx[ByXxx]
- 批量查询 listXxx[ByXxx]
- 分页查询 pageQueryXxx[ByXxx]
- 计次查询 countQueryXxx[ByXxx]

2. 新增
- create[Xxx]

3. 修改
- updateById

4. 删除
- deleteById

```
