package com.ddmc.promoequity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/9/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PropsInfo extends EquitySendBase {
    /**
     * 风控信息
     */
    private RiskInfo riskInfo;
    /**
     * 券信息
     */
    private CouponInfo couponInfo;
    /**
     * 券包信息
     */
    private CouponPackageInfo couponPackageInfo;
    /**
     * 积分信息
     */
    private PointInfo pointInfo;
    /**
     * 营销平台信息
     */
    private PromotionInfo promotionInfo;

    private StuffInfo stuffInfo;

    private DiscountGoodsInfo discountGoodsInfo;

    private BalanceInfo balanceInfo;

    /**
     * 虚拟步数
     */
    private VirtualStepInfo virtualStepInfo;
    /**
     * 微信代金券
     */
    private CashCouponInfo cashCouponInfo;

    /**
     * 积分兑换会员权益信息
     */
    private PointExchangeVipInfo exchangeVipInfo;

    public PropsInfo() {
    }

    public PropsInfo(CouponInfo couponInfo) {
        this.couponInfo = couponInfo;
    }

    public PropsInfo(CouponPackageInfo couponPackageInfo) {
        this.couponPackageInfo = couponPackageInfo;
    }

    public PropsInfo(PointInfo pointInfo) {
        this.pointInfo = pointInfo;
    }

    public PropsInfo(PromotionInfo promotionInfo) {
        this.promotionInfo = promotionInfo;
    }

    public PropsInfo(StuffInfo stuffInfo) {
        this.stuffInfo = stuffInfo;
    }

    public PropsInfo(DiscountGoodsInfo discountGoodsInfo) {
        this.discountGoodsInfo = discountGoodsInfo;
    }

    public PropsInfo(BalanceInfo balanceInfo) {
        this.balanceInfo = balanceInfo;
    }


}
