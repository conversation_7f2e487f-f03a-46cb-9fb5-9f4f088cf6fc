package com.ddmc.promoequity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/10/27
 */
@Data
public class BalanceInfo {
    /**
     * 【propInfo】余额卡id
     * */
    private String cardId;
    /**
     * 【propInfo】余额卡名称
     * */
    private String cardName;

    /**
     * 【extendInfo】余额卡实际发放余额
     * */
    private BigDecimal cardPrice;
    /**
     * 【extendInfo】余额卡发放bizNo
     * */
    private String bizNo;

    /**
     * 业务场景
     * {@link com.ddmc.promoequity.enums.BalanceBizSceneEnums}
     */
    private String bizScene;

    /**
     * 短信内容
     */
    private SmsInfo smsInfo;
}
