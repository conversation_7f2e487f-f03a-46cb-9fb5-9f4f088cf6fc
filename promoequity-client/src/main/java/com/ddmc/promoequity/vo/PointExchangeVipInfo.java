package com.ddmc.promoequity.vo;

import lombok.Data;

/**
 * 积分兑换会员权益 相关属性
 *
 * <AUTHOR>
 * @date 2023/10/31
 */
@Data
public class PointExchangeVipInfo {

    /**
     * 来源
     */
    private Integer source;

    /**
     * 兑换 vip天数
     */
    private Integer exchangeVipDays;

    /**
     * 消耗积分数量
     */
    private Integer needPointNum;

    /**
     * vip会员的生成的订单号
     */
    private String vipOrderNumber;

}
