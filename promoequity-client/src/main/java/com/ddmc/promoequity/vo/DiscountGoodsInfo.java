package com.ddmc.promoequity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/10/26
 */
@Data
public class DiscountGoodsInfo {

    /**
     * 最小折扣
     */
    private BigDecimal discountMin;
    /**
     * 最大折扣
     */
    private BigDecimal discountMax;


    /**
     * 商品id
     */
    private String goodsId;
    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品有效期开始时间
     */
    private Long startTime;

    /**
     * 商品有效期结束时间
     */
    private Long endTime;

    /**
     * 【extendsInfo】用户商品id
     */
    private Long userGoodsId;

    private Integer status;


}
