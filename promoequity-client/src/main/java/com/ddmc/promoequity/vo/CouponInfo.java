package com.ddmc.promoequity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/9/26
 */
@Data
public class CouponInfo {
    /**
     * 【propInfo】券id
     */
    private String ticketId;
    /**
     * 【propInfo】券名称
     */
    private String ticketName;

    private Long startTime;
    private Long expireTime;


    /**
     * 【extendInfo】用户券id
     */
    private String userTicketId;
    /**
     * 【extendInfo】用户id
     */
    private String userId;

    @ApiModelProperty("用户优惠券是否已读，控制未读提示和天降等。scene in (12,17,108) 无论 isRead 传值是什么，券平台写死为已读；其他 scene 取传值，为空默认为未读")
    private Boolean isRead;
}
