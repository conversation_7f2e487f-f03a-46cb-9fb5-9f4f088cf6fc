package com.ddmc.promoequity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/4/8 17:39
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskInfo {
    @ApiModelProperty("风控场景")
    private String scene_code;

    @ApiModelProperty("用户id")
    private String user_id;

    @ApiModelProperty("用户手机号，明文")
    private String user_mobile;

    /**
     * 字段获取方式文档：https://cfl.corp.100.me/pages/viewpage.action?pageId=9511653
     */
    @ApiModelProperty("叮咚设备指纹")
    private String device_id;

    @ApiModelProperty("数美设备指纹token")
    private String device_token;

    @ApiModelProperty("设备型号")
    private String device_model;

    @ApiModelProperty("设备名称")
    private String device_name;

    @ApiModelProperty("APP版本号")
    private String api_version;

    @ApiModelProperty("客户端来源")
    private String app_client_id;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("定位经度")
    private String longitude;

    @ApiModelProperty("定位纬度")
    private String latitude;

    @ApiModelProperty("定位城市编号")
    private String city_number;

    @ApiModelProperty("服务站ID")
    private String station_id;

    @ApiModelProperty("User-Agent HTTP头")
    private String user_agent;

    @ApiModelProperty("Referer HTTP头")
    private String referer;

    @ApiModelProperty("调用方接口")
    private String url;

    @ApiModelProperty("系统版本")
    private String os_version;

    @ApiModelProperty("广告标识符")
    private String idfa;

    @ApiModelProperty("应用开发商标识符")
    private String idfv;

    @ApiModelProperty("wifi名称")
    private String wifi_name;

    @ApiModelProperty("wifi_mac")
    private String wifi_mac;

    @ApiModelProperty("手机序列号")
    private String imei;

    @ApiModelProperty("手机序列号")
    private String imsi;

    @ApiModelProperty("国内手机序列号")
    private String oaid;
}
