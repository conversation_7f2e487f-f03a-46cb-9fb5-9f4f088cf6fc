package com.ddmc.promoequity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/9/27
 */
@Data
public class ExtendInfo {

    private BigDecimal bizCount;

    /**
     * 券信息
     */
    private CouponInfo couponInfo;

    /**
     * 券包信息
     */
    private CouponPackageInfo couponPackageInfo;

    /**
     * 积分信息
     */
    private PointInfo pointInfo;

    private StuffInfo stuffInfo;

    private DiscountGoodsInfo discountGoodsInfo;
    private BalanceInfo balanceInfo;
    private VirtualStepInfo virtualStepInfo;

    /**
     * 代金券
     */
    private CashCouponInfo cashCouponInfo;

    /**
     * 兑换会员的vip订单流水号
     */
    private PointExchangeVipInfo exchangeVipInfo;

    public ExtendInfo() {
    }

    public ExtendInfo(CouponInfo couponInfo) {
        this.couponInfo = couponInfo;
    }

    public ExtendInfo(CouponPackageInfo couponPackageInfo) {
        this.couponPackageInfo = couponPackageInfo;
    }

    public ExtendInfo(PointInfo pointInfo) {
        this.pointInfo = pointInfo;
    }

    public ExtendInfo(StuffInfo stuffInfo) {
        this.stuffInfo = stuffInfo;
    }

    public ExtendInfo(DiscountGoodsInfo discountGoodsInfo) {
        this.discountGoodsInfo = discountGoodsInfo;
    }

    public ExtendInfo(BalanceInfo balanceInfo) {
        this.balanceInfo = balanceInfo;
    }

    public ExtendInfo(VirtualStepInfo virtualStepInfo) {
        this.virtualStepInfo = virtualStepInfo;
    }

    public ExtendInfo(CashCouponInfo cashCouponInfo) {
        this.cashCouponInfo = cashCouponInfo;
    }

    public ExtendInfo(PointExchangeVipInfo exchangeVipInfo) {
        this.exchangeVipInfo = exchangeVipInfo;
    }
}
