package com.ddmc.promoequity.client;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.request.PointsOptionRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2022/05/12
 */
@FeignClient(name = "promoequity-service", url = "${feign.service.url.promoequity:}", contextId = "equityPointClient",
        path = "/promoequity-service/client/equity/point/v1")
public interface EquityPointClient {

    /**
     * 增加积分
     * @param request
     * @return
     */
    @PostMapping("/increasePoint")
    ResponseBaseVo<Boolean> increasePoint(@RequestBody PointsOptionRequest request);
    /**
     * 扣减积分
     * @param request
     * @return
     */
    @PostMapping("/decreasePoint")
    ResponseBaseVo<Boolean> decreasePoint(@RequestBody PointsOptionRequest request);
    /**
     * 查询用户总积分
     * @param userId
     * @return
     */
    @GetMapping("/getUserTotalPoint")
    ResponseBaseVo<Integer> getUserTotalPoint(@RequestParam("userId") String userId);
    /**
     * 奖励积分
     * @param request
     * @return
     */
    @PostMapping("/awardPoint")
    ResponseBaseVo<Boolean> awardPoint(@RequestBody PointsOptionRequest request);
    /**
     * 积分兑换
     * @param request
     * @return
     */
    @PostMapping("/pointExchange")
    ResponseBaseVo<Boolean> pointExchange(@RequestBody PointsOptionRequest request);
}
