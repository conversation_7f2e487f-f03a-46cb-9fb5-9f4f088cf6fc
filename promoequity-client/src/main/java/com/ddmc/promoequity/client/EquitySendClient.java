package com.ddmc.promoequity.client;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.MorePageResultDTO;
import com.ddmc.promoequity.dto.SendEquityResult;
import com.ddmc.promoequity.dto.request.SendEquityRequest;
import com.ddmc.promoequity.dto.request.SendRecordRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "promoequity-service", url = "${feign.service.url.promoequity:}", contextId = "equityClient",
        path = "/promoequity-service/client/equity/")
public interface EquitySendClient {

    /**
     * 发送权益
     *
     * @param request
     * @return
     */
    @PostMapping("send")
    ResponseBaseVo<SendEquityResult> sendEquity(@RequestBody SendEquityRequest request);

    /**
     * 发送记录
     *
     * @param request
     * @return
     */
    @PostMapping("/sendRecord")
    ResponseBaseVo<MorePageResultDTO<SendEquityResult>> sendRecord(@RequestBody SendRecordRequest request);
}
