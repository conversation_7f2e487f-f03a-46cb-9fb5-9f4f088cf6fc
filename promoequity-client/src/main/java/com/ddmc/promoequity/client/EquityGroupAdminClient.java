package com.ddmc.promoequity.client;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.EquityGroupDTO;
import com.ddmc.promoequity.dto.PageDTO;
import com.ddmc.promoequity.dto.request.QueryEquityGroupListRequest;
import com.ddmc.promoequity.dto.request.UpdateEquityGroupStateRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "promoequity-service", url = "${feign.service.url.promoequity:}", contextId =
        "equityGroupAdminClient", path = "/promoequity-service/admin/equity_group")
public interface EquityGroupAdminClient {
    /**
     * 查询权益组列表
     *
     * @param queryEquityListRequest
     * @return
     */
    @PostMapping("queryList")
    public ResponseBaseVo<PageDTO<EquityGroupDTO>> queryList(
            @RequestBody QueryEquityGroupListRequest queryEquityListRequest);

    /**
     * 查询权益组明细
     *
     * @param groupId
     * @return
     */
    @GetMapping("detail")
    public ResponseBaseVo<EquityGroupDTO> detail(@RequestParam("groupId") Long groupId);

    /**
     * 创建或修改权益组
     *
     * @param equityDTO
     * @return
     */
    @PostMapping("saveOrUpdate")
    public ResponseBaseVo<EquityGroupDTO> saveOrUpdate(@RequestBody EquityGroupDTO equityDTO);

    /**
     * 发布
     *
     * @param request
     * @return
     */
    @PostMapping("publish")
    public ResponseBaseVo publish(@RequestBody UpdateEquityGroupStateRequest request);

    /**
     * 废弃
     *
     * @param request
     * @return
     */
    @PostMapping("abandon")
    public ResponseBaseVo abandon(@RequestBody UpdateEquityGroupStateRequest request);
}
