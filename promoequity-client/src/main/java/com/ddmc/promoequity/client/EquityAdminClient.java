package com.ddmc.promoequity.client;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.promoequity.dto.*;
import com.ddmc.promoequity.dto.request.*;
import com.ddmc.promoequity.vo.BalanceInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "promoequity-service", url = "${feign.service.url.promoequity:}", contextId = "equityAdminClient"
        , path = "/promoequity-service/admin/equity")
public interface EquityAdminClient {
    @PostMapping("queryList")
    ResponseBaseVo<PageDTO<EquityDTO>> queryList(@RequestBody QueryEquityListRequest queryEquityListRequest);

    /**
     * 根据权益id查询明细
     *
     * @param equityId
     * @return
     */
    @GetMapping("detail")
    ResponseBaseVo<EquityDTO> detail(@RequestParam("equityId") Long equityId);

    /**
     * 根据权益id列表查询明细列表
     *
     * @param queryEquitysRequest
     * @return
     */
    @PostMapping("listDetail")
    ResponseBaseVo<List<EquityDTO>> listDetail(@RequestBody QueryEquitysRequest queryEquitysRequest);

    /**
     * （不推荐使用）使用 saveOrUpdateV1
     *
     * @param equityDTO
     * @return
     */
    @Deprecated
    @PostMapping("saveOrUpdate")
    ResponseBaseVo<EquityDTO> saveOrUpdate(@RequestBody EquityDTO equityDTO);

    /**
     * 新接口创建或修改权益
     *
     * @param request
     * @return
     */
    @PostMapping("saveOrUpdateV1")
    ResponseBaseVo<EquityDTO> saveOrUpdateV1(@RequestBody SaveOrUpdateEquityRequest request);

    /**
     * 权益发布
     *
     * @param request
     * @return
     */
    @PostMapping("publish")
    ResponseBaseVo publish(@RequestBody UpdateEquityStateRequest request);

    /**
     * 权益废弃
     *
     * @param request
     * @return
     */
    @PostMapping("abandon")
    ResponseBaseVo abandon(@RequestBody UpdateEquityStateRequest request);

    /**
     * 查找权益可用配置
     *
     * @param request
     * @return
     */
    @PostMapping("queryTagList")
    ResponseBaseVo<List<SelectLabelDTO>> queryTagList(@RequestBody QueryEquityLabelRequest request);

    /**
     * 权益发放统计
     *
     * @param request
     * @return
     */
    @PostMapping("equityStatistics")
    ResponseBaseVo<EquityStatisticsGroupDTO> equityStatistics(@RequestBody QueryStatisticsRequest request);

    /**
     * 权益发放统计bizCount
     *
     * @param request
     * @return
     */
    @PostMapping("bizCountRecordStatistics")
    ResponseBaseVo<EquityRecordBizCountStatisticDTO> bizCountRecordStatistics(@RequestBody EquityRecordBizCountStatisticDTO request);

    /**
     * 查询余额列表
     * @return
     */
    @GetMapping(value = "queryBalanceList")
    ResponseBaseVo<List<BalanceInfo>> queryBalanceList();

    /**
     * 查询用户权益信息
     * @param request
     * @return
     */
    @PostMapping("queryUserEquityInfo")
    ResponseBaseVo<List<UserEquityResult>> queryUserEquityInfo(@RequestBody List<QueryUserEquityRequest> request);

}
