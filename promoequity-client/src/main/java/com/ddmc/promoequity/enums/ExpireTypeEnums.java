package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/9/22
 */
@AllArgsConstructor
@Getter
public enum ExpireTypeEnums {
    UN_LIMIT("无限制"),
    LIMIT("限制");
    private String desc;

    public static ExpireTypeEnums get(String name) {
        return Arrays.stream(ExpireTypeEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}
