package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/11/7
 */
@AllArgsConstructor
@Getter
public enum EquityStatus {
    // 未使用
    NOUSE(0, 1, 0),
    // 已使用
    USED(1, 4, 1),
    // 过期
    OVERDUE(2, 3, 2);

    private Integer status;
    /**
     * coupon状态 -1 已删除 0 未激活 1 未使用 2 无效 3 已过期 4 已使用
     */
    private Integer couponStatus;
    /**
     * 商品折扣品状态 0 激活 1 核销完成 2 过期 3 关闭
     */
    private Integer goodsStatus;

    public static EquityStatus getByCouponStatus(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(EquityStatus.values()).filter(o -> o.getCouponStatus().intValue() == value.intValue()).findFirst().orElse(null);
    }

    public static EquityStatus getByGoodsStatus(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(EquityStatus.values()).filter(o -> o.getGoodsStatus().intValue() == value.intValue()).findFirst().orElse(null);
    }
}
