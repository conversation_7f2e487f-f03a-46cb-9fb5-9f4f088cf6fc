package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/9/22
 */
@AllArgsConstructor
@Getter
public enum BizLineEnums {
    PROMOTION("营销活动");
    private String desc;

    public static BizLineEnums get(String name) {
        return Arrays.stream(BizLineEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}
