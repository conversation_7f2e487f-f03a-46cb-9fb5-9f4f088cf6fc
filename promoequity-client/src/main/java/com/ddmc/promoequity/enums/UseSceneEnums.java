package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/9/22
 */
@AllArgsConstructor
@Getter
public enum UseSceneEnums {
    PROMOTION("营销活动");
    private String desc;

    public static UseSceneEnums get(String name) {
        return Arrays.stream(UseSceneEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}