package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */
@AllArgsConstructor
@Getter
public enum BalanceBizSceneEnums {

    DOUBLE_11_FREE_ORDER("双十一免单活动"),

    SINGLE_FREE_ORDER("单品免单"),
    WHOLE_FREE_ORDER("整单免单");

    private String desc;

    public static BalanceBizSceneEnums get(String name) {
        return Arrays.stream(BalanceBizSceneEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}
