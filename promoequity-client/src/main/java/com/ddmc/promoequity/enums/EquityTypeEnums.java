package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/9/22
 */
@AllArgsConstructor
@Getter
public enum EquityTypeEnums {
    COUPON("优惠券"),
    BALANCE("余额"),
    AUTO_BALANCE("自定义余额"),
    POINTS("积分"),
    EMPTY("空奖"),
    COUPONS("优惠券包"),
    STUFF("实物"),
    DISCOUNT_GOODS("商品折扣品"),
    PROMOTION("活动次数"),
    VIRTUAL_STEP("虚拟步数"),
    CASH_COUPON("代金券"),
    VIP("会员");
    private String desc;

    public static EquityTypeEnums get(String name) {
        return Arrays.stream(EquityTypeEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}