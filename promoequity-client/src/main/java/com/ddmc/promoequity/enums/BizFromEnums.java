package com.ddmc.promoequity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/9/22
 */
@AllArgsConstructor
@Getter
public enum BizFromEnums {
    COUPON("券平台"),
    INTEGRATION("余额平台"),
    ACTIVITY("促销平台"),
    PROMOTION("活动营销平台");
    private String desc;

    public static BizFromEnums get(String name) {
        return Arrays.stream(BizFromEnums.values()).filter(item -> item.name().equals(name))
                .findFirst().orElse(null);
    }
}