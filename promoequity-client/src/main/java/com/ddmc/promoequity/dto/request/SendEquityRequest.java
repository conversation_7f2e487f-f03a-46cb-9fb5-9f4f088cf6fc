package com.ddmc.promoequity.dto.request;

import com.ddmc.promoequity.vo.PropsInfo;
import lombok.Data;

/**
 * 权益发放请求参数
 *
 * <AUTHOR>
 */
@Data
public class SendEquityRequest {
    /**
     * 权益id
     */
    private Long equityId;
    /**
     * 流水号，唯一
     */
    private String bizNo;
    /**
     * 系统号（需要申请白名单）
     */
    private String bizFrom;

    /**
     * 业务统计关键字段（比如：活动id）
     */
    private String bizKey;
    /**
     * 用户id
     */
    private String uid;

    /**
     * 场景
     */
    private Integer scene;

    /**
     * 站点id
     */
    private String stationId;

    /**
     * 权益次数
     */
    private Integer equityCnt;

    /**
     * 描述
     */
    private String description;

    private PropsInfo propsInfo;

}
