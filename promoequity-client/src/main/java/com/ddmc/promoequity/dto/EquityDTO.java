package com.ddmc.promoequity.dto;

import com.ddmc.promoequity.vo.PropsInfo;
import com.ddmc.vouchercore.client.dto.TicketDTO;
import com.ddmc.vouchercore.client.dto.TicketPackageDTO;
import lombok.Data;

@Data
public class EquityDTO {
    private Long equityId;
    private String equityType;
    private String equityName;
    private String bizKey;
    private String description;
    private String creator;
    private Long publishTime;
    private Long createTime;
    private Long updateTime;
    private String status;

    private PropsInfo propsInfo;
    //额外扩展信息
    /**
     * 当equityType 为COUPON的时候：
     *
     * @see com.ddmc.vouchercore.client.dto.TicketDTO
     */
    private String extendsJsonInfo;


    private TicketDTO ticketDTO;
    private TicketPackageDTO ticketPackageDTO;
}
