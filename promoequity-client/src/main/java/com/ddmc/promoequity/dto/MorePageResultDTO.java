package com.ddmc.promoequity.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MorePageResultDTO<T> {
    private List<T> rows;
    private boolean hashMore;
    private Long nextKey;

    public static MorePageResultDTO empty() {
        MorePageResultDTO result = new MorePageResultDTO();
        result.setRows(new ArrayList());
        result.setHashMore(false);
        return result;
    }
}
