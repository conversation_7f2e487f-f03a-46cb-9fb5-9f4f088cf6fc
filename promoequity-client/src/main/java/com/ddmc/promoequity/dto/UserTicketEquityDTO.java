package com.ddmc.promoequity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserTicketEquityDTO {
    private String id;
    private String ticket;
    private String name;
    private Integer type;
    private BigDecimal money;
    private BigDecimal discount;
    private String description;
    private BigDecimal pay_min;
    private Integer status;
    private Long use_time;
    private Long expire_time;
    private Long start_time;
    private Integer is_invisible;

    // 新增的数据
    @ApiModelProperty("0 注册送券 1邀请码送券 2普通活动送券 3分享送券 4管理员发放 5分享红包发放 6活动返券" +
            "7超时赔付 8分拣缺货歉意券 9新年红包 10 用户召回 11 春节抽奖 12 用户vip领取 13 用户充值有礼" +
            "14分享累进奖励券 15集卡兑奖活动 16app领券 17优惠券兑换码活动 18转盘抽奖 19种树" +
            "20新手活动 21签到获取 22用户价值挖掘发券 23第三方发券 24邀请有礼")
    private String scene;

    @ApiModelProperty("使用场景")
    private String use_scene;

    @ApiModelProperty("关联用户id")
    private String user;

    @ApiModelProperty("关联coupon_ticket表")
    private TicketEquityDTO coupon_ticket;

    @ApiModelProperty("创建时间")
    private Long create_time;

    @ApiModelProperty("更新时间")
    private Long update_time;

    @ApiModelProperty("是否新用户券0 不是 1 是")
    private Integer is_new_user_ticket;

    @ApiModelProperty("是否vip可用")
    private boolean isVipTicket;

}
