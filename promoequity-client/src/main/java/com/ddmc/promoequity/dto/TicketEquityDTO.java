package com.ddmc.promoequity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
public class TicketEquityDTO {
    
    private String id;

    /**
     * ID号，由字母和数字随机自动组合成4位，且唯一，例子YH01
     */
    private String id_number;

    /**
     * 券类型1 满减券 2 赠品券 3 折扣券
     */
    private Integer type;

    /**
     * 券名
     */
    private String name;

    /**
     * 面值
     */
    private BigDecimal money;

    /**
     * 折扣 type == 3 折扣券。0.01 ~ 9.99 折扣
     */
    private BigDecimal discount;

    /**
     * 使用门槛-最低消费
     */
    private BigDecimal pay_min;

    /**
     * 使用场景(废弃)
     */
    private String use_scene;

    /**
     * 有效期类型0固定有效期1相对有效期(按天)2相对有效期(按小时) 3 相对有效期(按自然日) 4 相对有效期(按自然月)
     */
    private Integer valid_type;

    /**
     * 有有效期类型为1,2,3,4: 优惠券有效期
     */
    private Integer valid_days;

    /**
     * 有效期类型为0: 有效期-开始时间
     */
    private String begin_time;

    /**
     * 有效期类型为0:有效期-结束时间
     */
    private String end_time;

    /**
     * 有效期类型为1,2,3,4: 优惠券生效时间
     */
    private Integer start_days;

    /**
     * 图片
     */
    private String image;

    /**
     * 详情描述
     */
    private String description;

    /**
     * 详情描述
     */
    private String description_new;


    /**
     * 添加人
     */
    private String operator;

    /**
     * 备注
     */
    private String mark;

    /**
     * 状态 0 新建; 1 已发布; 2 已开始; 3 作废; 4 过期;5 已确认 财务确认
     */
    private Integer status;

    private Long create_time;

    private Long update_time;

    private Long cancel_time;

    private String h5_url;

    private String wxapp_url;

    private String native_url;

    private String aliapp_url;

    /**
     * 是否新用户券
     */
    private Integer is_new_user_ticket;

    /**
     * 是否仅限领取手机号使用
     */
    private Integer is_draw_user_ticket;

    /**
     * 禁止与礼品共用
     */
    private Integer is_ban_balance_ticket;

    /**
     * 是否需要监控
     */
    private Integer need_monitor;

    /**
     * 限制平台
     */
    private List<String> limit_platform;

    /**
     * 商品包id
     */
    private String product_map_id;

    /**
     * 规则类型: 0商品类型 1包类型
     */
    private Integer rule_type;

    private List<String> new_product_map_ids;

    private List<String> tags;

    /**
     * 是否隐藏面额
     */
    private Integer is_hide_money;

    /**
     * 0 不修改原生链接(不跳转商品包)，1 修改原生链接为优惠券包链接 (跳转商品包)
     */
    private Integer jump_product_package;

    /**
     * push标题
     */
    private String push_title;

    /**
     * push内容
     */
    private String push_content;

    /**
     * 是否支持购买vip
     */
    private String allow_vip_card_id;

    private Integer allow_vip_days;

    private Object limit_address;

    private Object limit_address_multi;

    private List<String> gift_product_ids;

    /**
     * 业务场景，1买菜场景，2vip场景，3，早上好场景。(注意：目前只有"早上好"场景有值，目前其他场景为null)
     */
    private Integer business_scene;

    /**
     * 可用范围选择方式 1可用 2不可用(配合new_rules使用)
     */
    private Integer new_rules_select_type;

    /**
     * 优惠券展示描述，例如 满30元减5元，满20元6折
     */
    private String ticketDescName;

    /**
     * 是否全场通用，0不通用，1通用
     */
    private Integer isCommonTicket;
}
