package com.ddmc.promoequity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/11/5
 */
@Data
public class UserGoodsEquityDTO {
    private Long id;

    private String sourceId;

    private String source;

    private String userId;

    private String productId;

    private Long productOid;

    private Integer conditions;

    private BigDecimal benefits;

    private Integer limit;

    private Integer type;

    private String areaIds;

    private Integer status;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;
}
