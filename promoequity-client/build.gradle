version = '1.1.4-RELEASE'
jar.enabled = true
bootJar.enabled = false
dependencies {
    compile('org.springframework.cloud:spring-cloud-starter-openfeign')
    compileOnly('org.projectlombok:lombok:1.18.12')
    compile('com.ddmc:core:1.1.1-SNAPSHOT')
    compile('com.ddmc:utils:1.1.1-SNAPSHOT')
    compile('com.fasterxml.jackson.core:jackson-databind')
    compile('javax.ws.rs:jsr311-api:1.1.1')
    compile('org.apache.commons:commons-lang3:3.7')

    //券平台
//    compile('com.ddmc:vouchercore-client:1.0.9-SNAPSHOT')

    compile('com.ddmc:vouchercore-client:1.1.0-RELEASE')

    compile('org.apache.logging.log4j:log4j-api:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-core:2.20.0') { force = true }
    compile('org.apache.logging.log4j:log4j-to-slf4j:2.20.0') { force = true }
}
