#!/bin/sh
#显示一些基本信息和目录结构
echo "[onepaas]start begin(app=${APPRUNTIME_APPNAME})......"
echo "[onepaas]${APPRUNTIME_APPNAME} soacluster=${APPRUNTIME_ENV_CLUSTER}"
echo "[onepaas]${APPRUNTIME_APPNAME} /data directory"
tree -L 2 /data
echo "[onepaas]${APPRUNTIME_APPNAME} cat /data/${APPRUNTIME_APPNAME}/appspec.yaml"
cat /data/${APPRUNTIME_APPNAME}/appspec.yaml
echo "[onepaas]${APPRUNTIME_APPNAME} APPRUNTIME environment variable"
env| grep APPRUNTIME

#设置在容器环境中jvm xmx,xmn,mete占系统内存的百分比,后面脚本根据此比例计算xmx值
JVM_XMX_MEM_RATIO=75
JVM_XMN_MEM_RATIO=45
JVM_META_MEM_RATIO=10
JVM_META_MEM_LIMT=256

jar_file=`ls /data/${APPRUNTIME_APPNAME}/*.jar`
jar_name=`basename $jar_file`
env=$APPRUNTIME_ENV_CLUSTER;
dump_dir=/data/share/${APPRUNTIME_APPNAME}_dump.log

echo "[onepaas]${APPRUNTIME_APPNAME} jar_file=${jar_file}"
echo "[onepaas]${APPRUNTIME_APPNAME} jar_name=${jar_name}"

calc() {
  local formula="$1"
  shift
  echo "$@" | awk '
    function ceil(x) {
      return x % 1 ? int(x) + 1 : x
    }
    function log2(x) {
      return log(x)/log(2)
    }
    function max2(x, y) {
      return x > y ? x : y
    }
    function round(x) {
      return int(x + 0.5)
    }
    {print '"int(${formula})"'}
  '
}
container_core_limit() {
  local cpu_period_file="/sys/fs/cgroup/cpu/cpu.cfs_period_us"
  local cpu_quota_file="/sys/fs/cgroup/cpu/cpu.cfs_quota_us"
  if [ -r "${cpu_period_file}" ]; then
    local cpu_period="$(cat ${cpu_period_file})"
    if [ -r "${cpu_quota_file}" ]; then
      local cpu_quota="$(cat ${cpu_quota_file})"
      if [ ${cpu_quota:-0} -ne -1 ]; then
        echo $(calc 'ceil($1/$2)' "${cpu_quota}" "${cpu_period}")
      fi
    fi
  fi
}
container_max_memory() {
  local mem_file="/sys/fs/cgroup/memory/memory.limit_in_bytes"
  if [ -r "${mem_file}" ]; then
    local max_mem_cgroup="$(cat ${mem_file})"
    local max_mem_meminfo_kb="$(cat /proc/meminfo | awk '/MemTotal/ {print $2}')"
    local max_mem_meminfo="$(expr $max_mem_meminfo_kb \* 1024)"
    if [ ${max_mem_cgroup:-0} != -1 ]
    then
      echo "${max_mem_cgroup}"
    fi
  fi
}
init_java_major_version() {
    if [ -z "${JAVA_MAJOR_VERSION:-}" ]; then
        local full_version=""

        if [ -n "${JAVA_VERSION:-}" ]; then
            full_version="$JAVA_VERSION"
        elif [ -n "${JAVA_HOME:-}" ] && [ -r "${JAVA_HOME}/release" ]; then
            full_version="$(grep -e '^JAVA_VERSION=' ${JAVA_HOME}/release | sed -e 's/.*\"\([0-9.]\{1,\}\).*/\1/')"
        else
            full_version=$(java -version 2>&1 | head -1 | sed -e 's/.*\"\([0-9.]\{1,\}\).*/\1/')
        fi
        export JAVA_MAJOR_VERSION=$(echo $full_version | sed -e 's/[^0-9]*\(1\.\)\{0,1\}\([0-9]\{1,\}\).*/\2/')
    fi
}
init_container_vars() {
  local core_limit="$(container_core_limit)"
  if [ -n "${core_limit}" ]; then
    export CONTAINER_CORE_LIMIT="${core_limit}"
  fi

  local mem_limit="$(container_max_memory)"
  if [ -n "${mem_limit}" ]; then
    export CONTAINER_MAX_MEMORY="${mem_limit}"
  fi
}
ci_compiler_count() {
  local core_limit="$1"
  local log_cpu=$(calc 'log2($1)' "$core_limit")
  local loglog_cpu=$(calc 'log2(max2($1,1))' "$log_cpu")
  local count=$(calc 'max2($1*$2,1)*3/2' "$log_cpu" "$loglog_cpu")
  local c1_count=$(calc 'max2($1/3,1)' "$count")
  local c2_count=$(calc 'max2($1-$2,1)' "$count" "$c1_count")
  [ $(c2_disabled) = true ] && echo "$c1_count" || echo $(calc '$1+$2' "$c1_count" "$c2_count")
}
c2_disabled() {
  if [ -n "${CONTAINER_MAX_MEMORY:-}" ]; then
    # 300m
    if [ "${CONTAINER_MAX_MEMORY}" -le 314572800 ]; then
      echo true
      return
    fi
  fi
  echo false
}
jvm_calc_mem() {
  local max_mem="$1"
  local fraction="$2"

  local val=$(calc 'round($1*$2/100/1048576)' "${max_mem}" "${fraction}")
  echo "${val}"
}
jvm_cpu_options() {
  if [ "${JAVA_MAJOR_VERSION:-0}" -ge "10" ]; then
    return
  fi

  if [ -n "${CONTAINER_CORE_LIMIT:-}" ]; then
    if [ -z ${core_limit} ]; then
      core_limit="${CONTAINER_CORE_LIMIT}"
    fi
    echo "-XX:ParallelGCThreads=${core_limit} " \
         "-XX:ConcGCThreads=${core_limit} " \
         "-Djava.util.concurrent.ForkJoinPool.common.parallelism=${core_limit} " \
         "-XX:CICompilerCount=$(ci_compiler_count $core_limit)"
  fi
}


#给一组默认值
[ -z $mx_mem ] && mx_mem=512
[ -z $ms_mem ] && ms_mem=256
[ -z $mn_mem ] && mn_mem=256
[ -z $max_meta_mem ] && max_meta_mem=256

#识别容器特定环境变量识别是否在容器环境
if [ -n "${APPRUNTIME_CONTAINER_POD_IP}" ]; then
    init_container_vars
    init_java_major_version
    echo "[onepaas]${APPRUNTIME_APPNAME} container system info javaversion=${JAVA_MAJOR_VERSION},memory=${CONTAINER_MAX_MEMORY},cpu=${CONTAINER_CORE_LIMIT}"
    cpu_opt=$(jvm_cpu_options)
    JVM=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMX_MEM_RATIO}")
    mx_mem=$JVM
    ms_mem=$JVM
    mn_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMN_MEM_RATIO}")
    max_meta_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_META_MEM_RATIO}")
    if [ "${max_meta_mem}" -ge ${JVM_META_MEM_LIMT} ]; then
      max_meta_mem=${JVM_META_MEM_LIMT}
    fi

fi

#组合jvm参数
java_opts=" -server -Xms${ms_mem}M -Xmx${mx_mem}M -Xmn${mn_mem}M -Xss1M -XX:MetaspaceSize=${max_meta_mem}M -XX:MaxMetaspaceSize=${max_meta_mem}M $cpu_opt -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelInitialMarkEnabled -XX:+CMSParallelRemarkEnabled -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${dump_dir} -Duser.timezone=GMT+8 -Djava.security.egd=/dev/urandom -Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv4Addresses $opt"

#启动进程
echo "[onepaas]${APPRUNTIME_APPNAME} java_opts=${java_opts}"
echo "[onepaas]${APPRUNTIME_APPNAME} onepass_env_opts=${ONEPAAS_ENV_OPTS}"
echo "[onepaas]${APPRUNTIME_APPNAME} start runing......"
exec java -Dappname=${APPRUNTIME_APPNAME} ${java_opts} ${ONEPAAS_ENV_OPTS} -jar ${jar_file} --spring.profiles.active=${env} 2>&1