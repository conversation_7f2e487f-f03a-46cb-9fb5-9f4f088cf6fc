target/
!.mvn/wrapper/maven-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/
/.gradle/
*.log
config-cache/

.DS_Store
target
build
*.jar
logs/
out/
data
.dubbo
.dubbo.lock
.gradletasknamecache


#gradle wrapper功能
!gradle/wrapper/gradle-wrapper.jar

#自定义的 tmp 文件
*.tree.tmp
/.vscode/settings.json