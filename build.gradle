buildscript {
    apply from:"${rootDir}/gradle/include/ext.gradle"

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'//10秒钟，可自定义，如10,'minutes'10分钟，10,'hours'10小时
    }

    repositories {
        maven { url nexusUrl }
        maven { url "https://plugins.gradle.org/m2/" }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.8"
        classpath "com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}"
    }
}

allprojects {
    group = 'com.ddmc'
    version = '1.0.0-SNAPSHOT'

    repositories {
        mavenLocal()
        maven { url nexusUrl }
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
}

subprojects {
    apply from: "${rootDir}/gradle/include/public.gradle"

    apply plugin: 'com.ddmc.sdkVersionControl'
    MiddlewareConfig {
        // warn or error，默认warn，error直接抛异常阻断执行
        conflictSeverityLevel = "warn"
        // 默认为false，开启后执行checkConflict会在当前目录下生成conflict.txt文件，与console打印的内容一致
        conflictFileEnable = false
        // 需要排除检查的jar包，多个用分号分隔
        excludePackage ="org.eclipse;org.jetbrains.kotlin"
        // bom版本信息，格式：group:module:version，分号间隔，与引入依赖格式一致
        bomInfo = "com.ddmc:middleware-bom:${middlewareBomVersion};" +
                "com.ddmc:third-party-bom:${thirdPartyBomVersion};" +
                "com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}"
        // warn or error，默认warn，error直接抛异常阻断执行
        snapshotSeverityLevel = "warn"
    }
}

apply from: "${rootDir}/gradle/include/sonar.gradle"