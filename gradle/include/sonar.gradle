apply plugin: 'java'
apply plugin: "jacoco"
apply plugin: 'org.sonarqube'
 
jacoco {
    toolVersion = "0.8.1"
    reportsDir = file("$buildDir/reports/jacoco")
}
 
task codeCoverageReport(type: JacocoReport) {
    // Gather execution data from all subprojects
    executionData fileTree(project.rootDir.absolutePath).include("**/build/jacoco/*.exec")
 
    // Add all relevant sourcesets from the subprojects
    subprojects.each {
        sourceSets it.sourceSets.main
    }
 
    reports {
        xml.enabled true
        html.enabled true
        csv.enabled false
    }
}
 
codeCoverageReport.dependsOn {
    subprojects*.test
}
 
sonarqube {
    properties {
        property 'sonar.sourceEncoding', 'UTF-8'
        property 'sonar.projectName', 'promoequity'
        property 'sonar.projectKey', 'promoequity'
        property 'sonar.host.url', 'https://sonar.corp.100.me/'
        property 'sonar.login', 'promoequity'
        property "sonar.coverage.jacoco.xmlReportPaths", "${rootDir}/build/reports/jacoco/codeCoverageReport/codeCoverageReport.xml"
    }
}
 
tasks.sonarqube.dependsOn tasks.codeCoverageReport
