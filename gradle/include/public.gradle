apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'eclipse'
apply plugin: 'maven'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'maven-publish'
apply from: "${rootDir}/gradle/include/ext.gradle"

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
    imports {
        mavenBom "com.ddmc:middleware-bom:${middlewareBomVersion}"
        mavenBom "com.ddmc:third-party-bom:${thirdPartyBomVersion}"
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'//10秒钟，可自定义，如10,'minutes'10分钟，10,'hours'10小时
}

repositories {
    maven { url nexusUrl }
}

task sourcesJar(type: Jar, dependsOn: classes) {
    classifier = 'sources'
    from sourceSets.main.allSource
}

publishing {
    if (project.gradle.getGradleVersion() != '4.9') {
        throw new Exception('Your gradle version must be 4.9')
    }
    apply from: "${rootDir}/gradle/include/conf.gradle"

    publications{
        jar(MavenPublication) {
            // MavenPublication 中有一些属性，主要包括groupId，artifactId，version,from,artifact
            // 其中groupId，artifactId，version，artifact都是选填的，不填默认去取项目的相关信息；
            groupId project.group // 项目的group
            artifactId project.name //项目name
            version project.version
            //如果打成war包填写components.web，如果打成jar包填写components.java
            from components.java
            //artifact sourceJar
        }
    }

    repositories {
        maven {
            if (project.version.endsWith('-SNAPSHOT')) {
                url = nexusSnapshotUrl
            } else {
                url = nexusReleaseUrl
            }
            credentials {
                username nexusUsername
                password nexusPassword
            }
        }
    }
}

def getGitTagOrBranch() {
    if(project.hasProperty('ASSIGN_VERSION')){
        def assignVersion = ASSIGN_VERSION;
        assignVersion = assignVersion.trim()
        if(assignVersion != '') {
            return assignVersion
        }
    }
    def gitBranch_tmp = 'git log -n 1 --pretty=%d'.execute(null, project.rootDir).text.trim().split(',')

    def gitTag = null
    def gitOrigin = null
    for(subStr in gitBranch_tmp){
        subStr = subStr.trim()
        /* 如果有多个tag信息，那么以最后一个tag名为准，最好一次提交不要有多个tag  */
        if(subStr.indexOf('tag:')==0){
            gitTag = subStr.substring(4).trim()
            if(')'.equals(gitTag.substring(gitTag.length()-1))) {
                gitTag = gitTag.substring(0, gitTag.length()-1)
            }
        }
        if(gitOrigin==null && subStr.indexOf('HEAD')>-1 && subStr.indexOf('->')>-1) {
            gitOrigin = subStr.split('->')[1].trim();
        }
        if(gitOrigin==null && subStr.indexOf('origin/')==0){
            gitOrigin = subStr.substring(7).trim()
        }
    }
    def gitBranch = ''

    if(gitTag){
        gitBranch = gitTag.trim();
    }else if(gitOrigin){
        if(')'.equals(gitOrigin.substring(gitOrigin.length()-1))) {
            gitOrigin = gitOrigin.substring(0, gitOrigin.length()-1)
        }
        gitBranch = gitOrigin.trim()
    }
    return gitBranch.replaceAll("/","-");
}

jar {
    if (project.gradle.getGradleVersion() != '4.9') {
        throw new Exception('Your gradle version must be 4.9')
    }

    into("META-INF/maven/$project.group/$project.name") {
        from { generatePomFileForJarPublication }
        rename "pom-default.xml", "pom.xml"
    }

    def gitCommitUserMail = 'git log -1 --pretty=%ce'.execute(null, project.rootDir).text.trim()

    def gitBranch = getGitTagOrBranch()
    /* 获取branch与tag信息：end */
    //start
    //获取工程远程git地址
    def gitRemoteUrl = 'git remote -v'.execute(null, project.rootDir).text.trim().split(' ')[0].split('	')[1]
    //获取提交对象（commit）的完整哈希字串
    def gitRevision = 'git log -1 --pretty=%H'.execute(null, project.rootDir).text.trim()
    //获取提交对象的简短哈希字串
    def gitAbbRevision = 'git log -1 --pretty=%h'.execute(null, project.rootDir).text.trim()
    def timeStamp = new Date().format('yyyyMMddHHmmss')

    def props = new Properties()
    buildFile.withInputStream {
        stream -> props.load(stream)
    }
    if (props["version"]) {
        project.version = props["version"].trim().replaceAll("\'|\"","")
    }

    doFirst {
        manifest {
            attributes('Specification-Vendor': 'The Ddmc Corp',
                    "Created-By": 'Gradle ' + project.gradle.gradleVersion,
                    "Class-Path": configurations.compile.collect { it.getName() }.join(' '))

            attributes('gitCommitUserMail': gitCommitUserMail,
                    'gitBranch': gitBranch,
                    'gitRemoteUrl': gitRemoteUrl,
                    'gitRevision': gitRevision,
                    'gitAbbRevision': gitAbbRevision)

            attributes('Project': project.name,
                    'BuildTime': timeStamp,
                    'BaseLine': project.name + "-" + project.version,
                    'ProjectVersion': project.version)
        }
    }
    //end
    baseName = project.name
    version =  project.version + "_" + gitBranch
}

bootJar {
    if (project.gradle.getGradleVersion() != '4.9') {
        throw new Exception('Your gradle version must be 4.9')
    }

    def gitBranch = getGitTagOrBranch()
    version = project.version + "_" + gitBranch

    metaInf {
        from '/tmp/bom.properties'
        into '/META-INF/soa/'
    }
}
jar.doLast { task ->
    ant.checksum file: task.archivePath
}