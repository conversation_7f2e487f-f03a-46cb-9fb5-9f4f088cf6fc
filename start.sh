#!/bin/sh
jar_file=`ls /usr/src/*.jar`
jar_name=`basename $jar_file`
service_name=`echo $jar_name | awk -F- '{print $1"-"$2}'`
if [ -z $env ];then
  env="YE"
  apollo_env="FAT"
fi
[ -z $mx_mem ] && mx_mem=512
[ -z $ms_mem ] && ms_mem=256
[ -z $mn_mem ] && mn_mem=256
[ -z $max_meta_mem ] && max_meta_mem=256
[ -z $opt ] && opt=""
dump_dir=/data/${jar_name}_dump.log
if [ $env == "PE" ] || [ $env == "HZAPE" ] || [ $env == "SHT4PE" ] || [ $env == "PEV" ] || [ $env == "PEA" ];then
	skywalking_server="skywalking-service.srv.mc.dd"
else
	skywalking_server="skywalking-service.test.srv.mc.dd"
fi




if [ "x${without_sky}" == "xtrue" ];then
  extra_opt=""
else
  extra_opt="-javaagent:/usr/src/agent/jacocoagent.jar=includes=*,excludes=org.springframework.data.*,output=tcpserver,port=${jacoco_port=20001},address=0.0.0.0 -javaagent:/usr/src/agent/skywalking-agent.jar -Dskywalking.agent.service_name=${service_name}_${env} -Dskywalking.collector.backend_service=${skywalking_server}:11800"
fi


if [ -z "${apollo_appid}" -o -z "${apollo_server}" ]; then
        java_opts="-Denv=${apollo_env} -Dapollo.cluster=${env} -server -Xms${ms_mem}M -Xmx${mx_mem}M -Xmn${mn_mem}M -Xss1M -XX:MetaspaceSize=${max_meta_mem}M -XX:MaxMetaspaceSize=${max_meta_mem}M -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelInitialMarkEnabled -XX:+CMSParallelRemarkEnabled -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${dump_dir} -Duser.timezone=GMT+8 -Djava.security.egd=/dev/urandom -Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv4Addresses $opt ${extra_opt}"
else
        if [ -z "${apollo_cluster}" ]; then
            apollo_cluster_name=${env}
        else
            apollo_cluster_name=${apollo_cluster}
        fi
        java_opts="-Denv=${apollo_env} -Dapp.id=${apollo_appid} -Dapollo.meta=${apollo_server} -Dapollo.cluster=${apollo_cluster_name} -server -Xms${ms_mem}M -Xmx${mx_mem}M -Xmn${mn_mem}M -Xss1M -XX:MetaspaceSize=${max_meta_mem}M -XX:MaxMetaspaceSize=${max_meta_mem}M -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelInitialMarkEnabled -XX:+CMSParallelRemarkEnabled -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${dump_dir} -Duser.timezone=GMT+8 -Djava.security.egd=/dev/urandom -Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv4Addresses $opt ${extra_opt}"
fi




exec java ${java_opts} -jar ${jar_file} --spring.profiles.active=${env}
